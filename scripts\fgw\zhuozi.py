# https://fgw.nmg.gov.cn/zfxxgk/fdzdgknr/?gk=3
import re
import requests
from bs4 import BeautifulSoup
import pandas as pd
import datetime
from llm_utils import call_llm
from tqdm import tqdm
import os
import json
import time

# 添加selenium相关导入
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

BASE_URL = "https://fgw.nmg.gov.cn"
LIST_URL = f"{BASE_URL}/zfxxgk/fdzdgknr/?gk=3"

headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36"
}

FIELDS = [
    "来源站点", "部门", "级别", "标题", "简介", "发布时间", "截止时间", "发文机关", "主题", "索引号", "文号", "详情地址", "正文内容", "敏感数字（元，%）", "附件", "文件名及链接"
]

def fetch_detail(url, title="", doc_no="", pub_date="", create_date=""):
    """获取详情页内容"""
    resp = requests.get(url, headers=headers, timeout=10)
    resp.encoding = resp.apparent_encoding
    soup = BeautifulSoup(resp.text, "html.parser")
    
    # 从详情页获取标题
    title_elem = soup.find("p", class_="d_biaoti")
    if title_elem:
        title = title_elem.get_text(strip=True)
    
    # 获取索引号
    index_no = ""
    index_elem = soup.find("td", class_="IDXID")
    if index_elem:
        index_no = index_elem.get_text(strip=True)
    
    # 获取主题分类
    theme = ""
    theme_elems = soup.find_all("td")
    for i, td in enumerate(theme_elems):
        if td.get_text(strip=True) == "主题分类" and i+1 < len(theme_elems):
            theme = theme_elems[i+1].get_text(strip=True)
            break
    
    # 获取发文机关
    issuing_agency = ""
    agency_elems = soup.find_all("td")
    for i, td in enumerate(agency_elems):
        if td.get_text(strip=True) == "发布机构" and i+1 < len(agency_elems):
            issuing_agency = agency_elems[i+1].get_text(strip=True)
            break
    
    # 获取文号
    if not doc_no:
        doc_no_elems = soup.find_all("td")
        for i, td in enumerate(doc_no_elems):
            if td.get_text(strip=True) == "文　　号" and i+1 < len(doc_no_elems):
                doc_no = doc_no_elems[i+1].get_text(strip=True)
                break
    
    # 获取成文日期
    if not create_date:
        date_elems = soup.find_all("td")
        for i, td in enumerate(date_elems):
            if td.get_text(strip=True) == "成文日期" and i+1 < len(date_elems):
                create_date = date_elems[i+1].get_text(strip=True)
                break
    
    # 获取发布时间
    if not pub_date:
        pub_date_div = soup.find("div", id="d_laiyuan")
        if pub_date_div:
            pub_date_match = re.search(r"发布时间：(\d{4}-\d{2}-\d{2})", pub_date_div.get_text())
            if pub_date_match:
                pub_date = pub_date_match.group(1)
    
    # 获取正文内容
    content_div = soup.find("div", id="d_show")
    content = ""
    if content_div:
        content = content_div.get_text("\n", strip=True)
    
    # 敏感数字（元，%）
    sensitive = ", ".join(re.findall(r"[\d.]+\s*(?:元|%|％)", content))
    
    # 附件
    attachment = ""
    attachment_links = soup.find_all("a", href=True, string=re.compile(r"附件"))
    if attachment_links:
        for a in attachment_links:
            href = a.get("href")
            if href.startswith("./"):
                # 相对路径，需要处理
                base_url = "/".join(url.split("/")[:-1])
                full_href = f"{base_url}/{href[2:]}"
            else:
                full_href = href if href.startswith("http") else BASE_URL + href
            attachment += f"{a.get_text(strip=True)}: {full_href}, "
    
    # 如果没有找到附件链接，尝试查找PDF下载链接
    if not attachment:
        pdf_links = soup.find_all("a", class_="pdflink", href=True)
        if pdf_links:
            for a in pdf_links:
                href = a.get("href")
                full_href = href if href.startswith("http") else BASE_URL + href
                attachment += f"PDF下载: {full_href}, "
    
    # 详情地址
    detail_url = url
    
    # 文件名及链接 - 这里只存储标题，实际的超链接会在保存Excel时创建
    file_link = title
    
    # 组装数据行
    row = [
        "内蒙古自治区发展和改革委员会",  # 来源站点（固定值）
        "发改委",                      # 部门（固定值）
        "卓资",                        # 级别（固定值）
        title,                        # 标题
        "",                           # 简介（后面用大模型生成）
        pub_date,                     # 发布时间
        "",                           # 截止时间
        issuing_agency,               # 发文机关
        theme,                        # 主题
        index_no,                     # 索引号
        doc_no,                       # 文号
        detail_url,                   # 详情地址
        content,                      # 正文内容
        sensitive,                    # 敏感数字
        attachment.rstrip(", "),      # 附件
        file_link                     # 文件名及链接
    ]
    return row

def fetch_list():
    """获取列表页内容"""
    # 方法1: 使用Selenium获取动态加载的内容
    chrome_options = Options()
    chrome_options.add_argument('--headless')  # 无头模式
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    # 初始化WebDriver
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # 访问列表页
        driver.get(LIST_URL)
        
        # 等待表格加载完成
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.ID, "table2"))
        )
        
        # 获取页面源码
        page_source = driver.page_source
        soup = BeautifulSoup(page_source, "html.parser")
        
        # 方法2: 如果页面使用AJAX加载数据，可以尝试直接获取数据接口
        # 查找是否有数据接口的URL
        scripts = soup.find_all("script")
        data_url = None
        for script in scripts:
            script_text = script.string
            if script_text and "dataUrl" in script_text:
                match = re.search(r'dataUrl\s*=\s*[\'"]([^\'"]+)[\'"]', script_text)
                if match:
                    data_url = match.group(1)
                    break
        
        data = []
        
        # 如果找到数据接口，直接请求数据
        if data_url:
            full_data_url = data_url if data_url.startswith('http') else BASE_URL + data_url
            print(f"找到数据接口: {full_data_url}")
            
            # 获取数据
            response = requests.get(full_data_url, headers=headers)
            json_data = response.json()
            
            # 处理JSON数据
            # 这里需要根据实际返回的JSON结构进行调整
            items = json_data.get('data', {}).get('items', [])
            for item in items:
                title = item.get('title', '')
                href = item.get('link', '')
                doc_no = item.get('docNo', '')
                pub_date = item.get('publishDate', '')
                create_date = item.get('createDate', '')
                
                full_url = href if href.startswith('http') else BASE_URL + href
                
                try:
                    # 获取详情页内容
                    row = fetch_detail(full_url, title, doc_no, pub_date, create_date)
                    data.append(row)
                except Exception as e:
                    print(f"详情页解析失败: {full_url}, 错误: {e}")
        
        # 如果没有找到数据接口或接口请求失败，则从页面解析
        if not data:
            # 查找表格 - 处理多个table2的情况
            tables = soup.find_all("table", id="table2")
            if not tables:
                print("未找到任何 table2")
                return []
            
            print(f"找到 {len(tables)} 个 table2 表格")
            
            # 遍历所有找到的表格
            for table_idx, table in enumerate(tables):
                print(f"正在处理第 {table_idx+1} 个 table2 表格")
                
                # 遍历表格行
                tbody = table.find("tbody", id="dataList")
                if not tbody:
                    print(f"表格 {table_idx+1} 未找到 dataList，尝试直接查找tr")
                    rows = table.find_all("tr")
                else:
                    rows = tbody.find_all("tr")
                
                if not rows:
                    print(f"表格 {table_idx+1} 没有行数据")
                    continue
                    
                print(f"表格 {table_idx+1} 找到 {len(rows)} 行数据")
                
                for tr in rows:
                    tds = tr.find_all("td")
                    if len(tds) >= 5:
                        # 获取标题和链接
                        title_td = tds[1]
                        a_tag = title_td.find("a")
                        if a_tag:
                            title = a_tag.get_text(strip=True)
                            href = a_tag.get("href")
                            full_url = href if href.startswith("http") else BASE_URL + href
                            
                            # 获取文号 - 直接使用第3列的文本
                            doc_no = ""
                            if len(tds) > 2:
                                doc_no = tds[2].get_text(strip=True)
                                print(f"从第3列提取到文号: {doc_no}")
                            
                            # 获取成文日期和发布日期
                            create_date = tds[3].get_text(strip=True) if len(tds) > 3 else ""
                            pub_date = tds[4].get_text(strip=True) if len(tds) > 4 else ""
                            
                            try:
                                # 获取详情页内容
                                print(f"正在获取详情: {title} - {full_url}")
                                row = fetch_detail(full_url, title, doc_no, pub_date, create_date)
                                data.append(row)
                            except Exception as e:
                                print(f"详情页解析失败: {full_url}, 错误: {e}")
    
    finally:
        # 关闭WebDriver
        driver.quit()
    
    # 用大模型生成简介
    print("\n正在为每条数据生成简介（50字以内摘要）...")
    for row in tqdm(data, desc="生成简介", ncols=80):
        content = row[12] if len(row) > 12 else ""
        summary = ""
        if content:
            try:
                prompt = "请为以下文章内容生成一个50字以内的简要摘要，不要超过50字：\n\n" + content
                summary = call_llm(prompt)
                # 确保摘要不超过50字
                if len(summary) > 50:
                    summary = summary[:47] + "..."
            except Exception as e:
                print(f"生成简介失败: {e}")
        row[4] = summary  # 简介列
    
    return data

def save_to_excel(data, filename=None):
    # 确保data目录存在
    data_dir = os.path.join(os.path.dirname(__file__), "data")
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)
    
    # 生成文件名
    if filename is None:
        now = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        filename = f"发改委-卓资-{now}.xlsx"
    
    # 完整文件路径
    filepath = os.path.join(data_dir, filename)
    
    # 创建DataFrame
    df = pd.DataFrame(data, columns=FIELDS)
    
    # 创建一个Excel writer对象
    writer = pd.ExcelWriter(filepath, engine='openpyxl')
    
    # 将DataFrame写入Excel
    df.to_excel(writer, index=False)
    
    # 获取工作表
    worksheet = writer.sheets['Sheet1']
    
    # 为"文件名及链接"列添加超链接
    for idx, row in enumerate(data, start=2):  # Excel行从2开始（1是标题行）
        title = row[3]  # 标题
        url = row[11]   # 详情地址
        cell = worksheet.cell(row=idx, column=16)  # 第16列是"文件名及链接"
        cell.value = title
        cell.hyperlink = url
        cell.style = 'Hyperlink'
    
    # 保存Excel
    writer.close()
    print(f"已保存到 {filepath}")

if __name__ == "__main__":
    try:
        print("开始抓取数据...")
        data = fetch_list()
        
        if not data:
            print("警告：未获取到任何数据！")
        else:
            print(f"成功获取 {len(data)} 条数据")
            
        # 保存数据到Excel
        save_to_excel(data)
        
        # 也可以保存为CSV备份
        data_dir = os.path.join(os.path.dirname(__file__), "data")
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)
        now = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        csv_path = os.path.join(data_dir, f"发改委-卓资-{now}.csv")
        
        
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()
# https://fgw.nmg.gov.cn/zfxxgk/fdzdgknr/?gk=3
