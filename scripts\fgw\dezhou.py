# http://fgw.shandong.gov.cn/col/col91104/index.html

import re
import requests
from bs4 import BeautifulSoup
import pandas as pd
import datetime
from llm_utils import call_llm
from tqdm import tqdm
import os

BASE_URL = "http://fgw.shandong.gov.cn"
LIST_URL = f"{BASE_URL}/col/col91104/index.html"

headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36"
}

FIELDS = [
    "来源站点", "部门", "级别", "标题", "简介", "发布时间", "截止时间", "发文机关", "主题", "索引号", "文号", "详情地址", "正文内容", "敏感数字（元，%）", "附件", "文件名及链接"
]

def fetch_detail(url):
    resp = requests.get(url, headers=headers, timeout=10)
    resp.encoding = resp.apparent_encoding
    soup = BeautifulSoup(resp.text, "html.parser")
    # 标题
    htitle = soup.find("div", class_="htitle")
    title = ""
    doc_no = ""
    if htitle:
        # 主标题
        title_tag = htitle.contents[0] if htitle.contents else ""
        title = title_tag.strip() if isinstance(title_tag, str) else htitle.get_text(strip=True)
        # 副标题（文号）
        p = htitle.find("p")
        if p:
            doc_no = p.get_text(strip=True)
    # 正文内容
    art_con = soup.find("div", class_="art_con")
    content = art_con.get_text("\n", strip=True) if art_con else ""
    # 敏感数字（元，%）
    sensitive = ", ".join(re.findall(r"[\d.]+\s*(?:元|%|％)", content))
    # 附件
    attachment = ""
    if art_con:
        attach_links = art_con.find_all("a", href=True)
        attach_urls = [BASE_URL + a['href'] if a['href'].startswith('/') else a['href'] for a in attach_links]
        attachment = ", ".join(attach_urls)
    # 发布时间
    pub_date = ""
    # 详情页有时正文下方有日期，也可能在别处
    date_match = re.search(r"(20\d{2}[年\-/]\d{1,2}[月\-/]\d{1,2}日?)", content)
    if date_match:
        pub_date = date_match.group(1)
    # 详情地址
    detail_url = url
    # 文件名及链接 - 这里只存储标题，实际的超链接会在保存Excel时创建
    file_link = title
    # 其他字段留空
    row = [
        "山东省发展和改革委员会",  # 来源站点（固定值）
        "发改委",                # 部门（固定值）
        "德州",                  # 级别（固定值）
        title,                  # 标题
        "",                     # 简介
        pub_date,               # 发布时间
        "",                     # 截止时间
        "",                     # 发文机关
        "",                     # 主题
        "",                     # 索引号
        doc_no,                 # 文号
        detail_url,             # 详情地址
        content,                # 正文内容
        sensitive,              # 敏感数字
        attachment,             # 附件
        file_link               # 文件名及链接
    ]
    return row

def fetch_list():
    resp = requests.get(LIST_URL, headers=headers, timeout=10)
    resp.encoding = resp.apparent_encoding
    soup = BeautifulSoup(resp.text, "html.parser")
    ul = soup.find("ul", class_="tab_conList")
    if not ul:
        print("未找到 tab_conList")
        return []
    data = []
    for li in ul.find_all("li", class_="clearfix"):
        a = li.find("a")
        date_span = li.find("span", class_="data")  # 从列表页获取日期
        if a:
            href = a.get("href")
            full_url = href if href.startswith("http") else BASE_URL + href
            try:
                row = fetch_detail(full_url)
                # 如果列表页有日期，优先使用列表页的日期
                if date_span:
                    list_date = date_span.get_text(strip=True)
                    if list_date:
                        row[5] = list_date  # 更新发布时间
                data.append(row)
            except Exception as e:
                print(f"详情页解析失败: {full_url}, 错误: {e}")
    # 用大模型生成简介
    for row in tqdm(data, desc="生成简介", ncols=80):
        content = row[11] if len(row) > 11 else ""
        summary = ""
        if content:
            summary = call_llm(content)
        row[4] = summary  # 简介列
    return data

def save_to_excel(data, filename=None):
    # 确保data目录存在
    data_dir = os.path.join(os.path.dirname(__file__), "data")
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)
    
    # 生成文件名
    if filename is None:
        now = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        filename = f"发改委-德州-{now}.xlsx"
    
    # 完整文件路径
    filepath = os.path.join(data_dir, filename)
    
    # 创建DataFrame
    df = pd.DataFrame(data, columns=FIELDS)
    
    # 创建一个Excel writer对象
    writer = pd.ExcelWriter(filepath, engine='openpyxl')
    
    # 将DataFrame写入Excel
    df.to_excel(writer, index=False)
    
    # 获取工作表
    worksheet = writer.sheets['Sheet1']
    
    # 为"文件名及链接"列添加超链接
    for idx, row in enumerate(data, start=2):  # Excel行从2开始（1是标题行）
        title = row[3]  # 标题
        url = row[11]   # 详情地址
        cell = worksheet.cell(row=idx, column=16)  # 第16列是"文件名及链接"
        cell.value = title
        cell.hyperlink = url
        cell.style = 'Hyperlink'
    
    # 保存Excel
    writer.close()
    print(f"已保存到 {filepath}")

if __name__ == "__main__":
    data = fetch_list()
    save_to_excel(data)

