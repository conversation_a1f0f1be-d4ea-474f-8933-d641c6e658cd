# 深圳市政策信息抓取脚本
# 需要爬取的两个链接:
# 1. https://sqzc.gd.gov.cn/sqzc/m/cms/policy/getPolicyListPage2?pageNumber=1&pageSize=10&keywords=%E7%A8%8E%E8%B4%B9%E5%87%8F%E5%85%8D&city=%E6%B7%B1%E5%9C%B3&publisher=
# 2. https://sqzc.gd.gov.cn/sqzc/m/cms/policy/getPolicyListPage2?pageNumber=1&pageSize=10&keywords=%E8%B5%84%E9%87%91%E7%94%B3%E6%8A%A5&city=%E6%B7%B1%E5%9C%B3&publisher=
# 详情页: https://sqzc.gd.gov.cn/sqzc/m/cms/policy/policyinfo/{id}

import re
import requests
from bs4 import BeautifulSoup
import pandas as pd
import datetime
from llm_utils import call_llm
from tqdm import tqdm
import os
import time
import json
import urllib.parse

BASE_URL = "https://sqzc.gd.gov.cn"
LIST_API = f"{BASE_URL}/sqzc/m/cms/policy/getPolicyListPage2"
DETAIL_URL_TEMPLATE = f"{BASE_URL}/sqzc/m/cms/policy/policyinfo/{{}}"

headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36",
    "Accept": "application/json, text/plain, */*",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
    "Referer": "https://sqzc.gd.gov.cn/"
}

FIELDS = [
    "来源站点", "部门", "级别", "标题", "简介", "发布时间", "截止时间", "发文机关", "主题", "索引号", "文号", "详情地址", "正文内容", "敏感数字（元，%）", "附件", "文件名及链接"
]

# 需要爬取的两个关键词
SEARCH_KEYWORDS = [
    "税费减免",  # %E7%A8%8E%E8%B4%B9%E5%87%8F%E5%85%8D
    "资金申报"   # %E8%B5%84%E9%87%91%E7%94%B3%E6%8A%A5
]

def fetch_detail(policy_id, title="", pub_date="", publisher="", debug=False):
    """获取政策详情页内容"""
    detail_url = DETAIL_URL_TEMPLATE.format(policy_id)

    try:
        resp = requests.get(detail_url, headers=headers, timeout=10)
        resp.encoding = 'utf-8'
        soup = BeautifulSoup(resp.text, "html.parser")

        if debug:
            print(f"    调试信息 - 详情页URL: {detail_url}")
            print(f"    调试信息 - 响应状态: {resp.status_code}")

        # 获取标题
        if not title:
            # 尝试多种标题选择器
            title_selectors = [
                "p.diagramTitle",
                "i#span_title",
                ".diagramTitle",
                "h1",
                "h2"
            ]
            for selector in title_selectors:
                title_elem = soup.select_one(selector)
                if title_elem:
                    title = title_elem.get_text(strip=True)
                    break

        # 获取发布时间
        if not pub_date:
            # 尝试多种时间选择器
            time_selectors = [
                "span:contains('发布时间') i",
                ".policyPublishDate",
                "span.policyPublishDate"
            ]
            for selector in time_selectors:
                if "contains" in selector:
                    # 使用BeautifulSoup的find方法处理contains
                    time_span = soup.find("span", string=lambda text: text and "发布时间" in text)
                    if time_span:
                        time_i = time_span.find("i")
                        if time_i:
                            pub_date = time_i.get_text(strip=True)
                            break
                else:
                    time_elem = soup.select_one(selector)
                    if time_elem:
                        pub_date = time_elem.get_text(strip=True)
                        break

            # 如果还没找到，尝试从文本中提取
            if not pub_date:
                time_pattern = r'发布时间\s*[：:]\s*(\d{4}-\d{2}-\d{2})'
                time_match = re.search(time_pattern, soup.get_text())
                if time_match:
                    pub_date = time_match.group(1)

        # 获取发布单位
        if not publisher:
            # 尝试多种发布单位选择器
            publisher_selectors = [
                "span:contains('发布单位') i",
                ".policySource",
                "i.policySource"
            ]
            for selector in publisher_selectors:
                if "contains" in selector:
                    # 使用BeautifulSoup的find方法处理contains
                    pub_span = soup.find("span", string=lambda text: text and "发布单位" in text)
                    if pub_span:
                        pub_i = pub_span.find("i")
                        if pub_i:
                            publisher = pub_i.get_text(strip=True)
                            break
                else:
                    pub_elem = soup.select_one(selector)
                    if pub_elem:
                        publisher = pub_elem.get_text(strip=True)
                        break

        # 获取正文内容
        content = ""
        content_selectors = [
            "div.articleContent",
            "div#policyinfo",
            ".articleContent"
        ]
        for selector in content_selectors:
            content_div = soup.select_one(selector)
            if content_div:
                content = content_div.get_text("\n", strip=True)
                break

        # 获取文号 - 从正文中提取
        doc_no = ""
        # 匹配深圳市各种文号格式
        doc_no_patterns = [
            r'深[〔［\[\(（]?\d{4}[〕］\]\)）]?\s*\d+\s*号',
            r'深政[〔［\[\(（]?\d{4}[〕］\]\)）]?\s*\d+\s*号',
            r'深发改[〔［\[\(（]?\d{4}[〕］\]\)）]?\s*\d+\s*号',
            r'深财[〔［\[\(（]?\d{4}[〕］\]\)）]?\s*\d+\s*号',
            r'深商务[〔［\[\(（]?\d{4}[〕］\]\)）]?\s*\d+\s*号',
            r'深科技[〔［\[\(（]?\d{4}[〕］\]\)）]?\s*\d+\s*号',
            r'深人社[〔［\[\(（]?\d{4}[〕］\]\)）]?\s*\d+\s*号',
            r'深工信[〔［\[\(（]?\d{4}[〕］\]\)）]?\s*\d+\s*号',
            r'深税[〔［\[\(（]?\d{4}[〕］\]\)）]?\s*\d+\s*号',
            r'深罗府[〔［\[\(（]?\d{4}[〕］\]\)）]?\s*\d+\s*号',
            r'深南府[〔［\[\(（]?\d{4}[〕］\]\)）]?\s*\d+\s*号',
            r'深福府[〔［\[\(（]?\d{4}[〕］\]\)）]?\s*\d+\s*号',
            r'深龙府[〔［\[\(（]?\d{4}[〕］\]\)）]?\s*\d+\s*号',
            r'深宝府[〔［\[\(（]?\d{4}[〕］\]\)）]?\s*\d+\s*号',
            r'深盐府[〔［\[\(（]?\d{4}[〕］\]\)）]?\s*\d+\s*号',
            r'深坪府[〔［\[\(（]?\d{4}[〕］\]\)）]?\s*\d+\s*号',
            r'深光府[〔［\[\(（]?\d{4}[〕］\]\)）]?\s*\d+\s*号',
            r'深大府[〔［\[\(（]?\d{4}[〕］\]\)）]?\s*\d+\s*号',
            r'深龙华府[〔［\[\(（]?\d{4}[〕］\]\)）]?\s*\d+\s*号',
            r'深前海[〔［\[\(（]?\d{4}[〕］\]\)）]?\s*\d+\s*号'
        ]

        for pattern in doc_no_patterns:
            doc_no_match = re.search(pattern, content)
            if doc_no_match:
                doc_no = doc_no_match.group(0)
                break

        # 敏感数字（元，%）
        sensitive = ", ".join(re.findall(r"[\d.]+\s*(?:元|%|％|万元|亿元|千万元|十万元)", content))

        # 获取附件信息
        attachment = ""
        # 尝试多种附件选择器
        attachment_selectors = [
            "div.relatedAccessory",
            "div.relatedAccessory.fileInfos.fileUrlList",
            ".relatedAccessory"
        ]

        for selector in attachment_selectors:
            attachment_div = soup.select_one(selector)
            if attachment_div and not attachment_div.has_attr("class") or "none" not in attachment_div.get("class", []):
                # 查找附件链接
                attachment_links = attachment_div.find_all("a", href=True)
                for a in attachment_links:
                    href = a.get("href")
                    if href:
                        full_href = href if href.startswith("http") else BASE_URL + href
                        attachment_text = a.get_text(strip=True)
                        attachment += f"{attachment_text}: {full_href}, "

                # 如果找到附件就跳出循环
                if attachment_links:
                    break

        # 如果没有找到附件链接，尝试查找附件文本信息
        if not attachment:
            attachment_texts = soup.find_all("p", class_="accessoryText")
            for p in attachment_texts:
                attachment_link = p.find("a", href=True)
                if attachment_link:
                    href = attachment_link.get("href")
                    full_href = href if href.startswith("http") else BASE_URL + href
                    attachment_text = attachment_link.get_text(strip=True)
                    attachment += f"{attachment_text}: {full_href}, "

        # 获取索引号（如果有的话）
        index_no = ""
        index_patterns = [
            r'索引号[：:]\s*([A-Za-z0-9\-]+)',
            r'文件编号[：:]\s*([A-Za-z0-9\-]+)',
            r'编号[：:]\s*([A-Za-z0-9\-]+)'
        ]
        for pattern in index_patterns:
            index_match = re.search(pattern, content)
            if index_match:
                index_no = index_match.group(1)
                break

        # 获取截止时间（如果有的话）
        deadline = ""
        deadline_patterns = [
            r'截止时间[：:]\s*(\d{4}-\d{2}-\d{2})',
            r'申报时间[：:]\s*(\d{4}年\d{1,2}月\d{1,2}日至\d{4}年\d{1,2}月\d{1,2}日)',
            r'受理时间[：:]\s*(\d{4}年\d{1,2}月\d{1,2}日至\d{4}年\d{1,2}月\d{1,2}日)'
        ]
        for pattern in deadline_patterns:
            deadline_match = re.search(pattern, content)
            if deadline_match:
                deadline = deadline_match.group(1)
                break

        # 发文机关
        issuing_agency = publisher

        # 文件名及链接
        file_link = title

        # 组装数据行
        row = [
            "深圳市政策平台",        # 来源站点
            "深圳市政府",           # 部门
            "深圳",                # 级别
            title,                # 标题
            "",                   # 简介（后面用大模型生成）
            pub_date,             # 发布时间
            deadline,             # 截止时间
            issuing_agency,       # 发文机关
            "",                   # 主题（在fetch_all_policies中会设置为关键词）
            index_no,             # 索引号
            doc_no,               # 文号
            detail_url,           # 详情地址
            content,              # 正文内容
            sensitive,            # 敏感数字
            attachment.rstrip(", "), # 附件
            file_link             # 文件名及链接
        ]

        if debug:
            print(f"    调试信息 - 提取结果:")
            print(f"      标题: {title[:50]}...")
            print(f"      发布时间: {pub_date}")
            print(f"      发布单位: {issuing_agency}")
            print(f"      文号: {doc_no}")
            print(f"      索引号: {index_no}")
            print(f"      截止时间: {deadline}")
            print(f"      附件数量: {len(attachment.split(',')) if attachment else 0}")
            print(f"      正文长度: {len(content)} 字符")
            print(f"      敏感数字: {sensitive[:100]}..." if sensitive else "      敏感数字: 无")

        return row

    except Exception as e:
        print(f"获取详情页失败: {detail_url}, 错误: {e}")
        return None

def fetch_policy_list(keyword, page_number=1, page_size=10):
    """获取政策列表"""
    params = {
        "pageNumber": page_number,
        "pageSize": page_size,
        "keywords": keyword,
        "city": "深圳",
        "publisher": ""
    }

    try:
        resp = requests.get(LIST_API, params=params, headers=headers, timeout=10)
        resp.encoding = 'utf-8'
        data = resp.json()

        if data.get("success") and data.get("data"):
            return data
        else:
            print(f"API返回错误: {data.get('msg', '未知错误')}")
            return None

    except Exception as e:
        print(f"获取政策列表失败: {e}")
        return None

def fetch_all_policies():
    """获取两个关键词的所有政策数据"""
    all_data = []

    for keyword in SEARCH_KEYWORDS:
        print(f"\n正在搜索关键词: {keyword}")
        page = 1

        while True:
            print(f"  正在获取第 {page} 页...")
            result = fetch_policy_list(keyword, page)

            if not result or not result.get("data"):
                print(f"  关键词 '{keyword}' 第 {page} 页无数据，结束搜索")
                break

            policies = result["data"]
            total = result.get("total", 0)
            page_size = result.get("pageSize", 10)

            print(f"  第 {page} 页获取到 {len(policies)} 条政策，总计 {total} 条")

            # 处理每个政策
            for i, policy in enumerate(policies, 1):
                policy_id = policy.get("id")
                title = policy.get("title", "")
                pub_date = policy.get("publishDate", "")
                publisher = policy.get("publisher", "")

                if policy_id:
                    print(f"    [{i}/{len(policies)}] 正在获取详情: {title[:50]}...")
                    # 对第一个政策启用调试模式，查看提取效果
                    debug_mode = (len(all_data) == 0)
                    row = fetch_detail(policy_id, title, pub_date, publisher, debug=debug_mode)
                    if row:
                        # 在数据中标记关键词来源
                        row[8] = keyword  # 主题字段用来标记关键词
                        all_data.append(row)

                    # 添加延迟，避免请求过快
                    time.sleep(1)

            # 检查是否还有下一页
            if len(policies) < page_size:
                print(f"  关键词 '{keyword}' 已获取完所有数据")
                break

            page += 1

            # 限制最大页数，避免无限循环（可根据需要调整）
            if page > 2:
                print(f"  关键词 '{keyword}' 已达到最大页数限制 (2页)")
                break

    return all_data

def generate_summaries(data):
    """为每条数据生成简介"""
    print("\n正在为每条数据生成简介（50字以内摘要）...")
    for row in tqdm(data, desc="生成简介", ncols=80):
        content = row[12]  # 正文内容
        if content:
            try:
                prompt = "请为以下文章内容生成一个50字以内的简要摘要，不要超过50字：\n\n" + content
                summary = call_llm(prompt)
                # 确保摘要不超过50字
                if len(summary) > 50:
                    summary = summary[:47] + "..."
                row[4] = summary  # 简介列
            except Exception as e:
                print(f"生成简介失败: {e}")
    return data

def save_to_excel(data):
    """保存数据到Excel"""
    if not data:
        print("没有数据可保存")
        return

    # 创建数据目录
    data_dir = os.path.join(os.path.dirname(__file__), "data")
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)

    # 生成文件名
    now = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    filename = f"深圳市政策-税费减免+资金申报-{now}.xlsx"

    # 完整文件路径
    filepath = os.path.join(data_dir, filename)

    # 创建DataFrame
    df = pd.DataFrame(data, columns=FIELDS)

    # 创建一个Excel writer对象
    writer = pd.ExcelWriter(filepath, engine='openpyxl')

    # 将DataFrame写入Excel
    df.to_excel(writer, index=False)

    # 获取工作表
    worksheet = writer.sheets['Sheet1']

    # 为"文件名及链接"列添加超链接
    for idx, row in enumerate(data, start=2):  # Excel行从2开始（1是标题行）
        title = row[3]  # 标题
        url = row[11]   # 详情地址
        cell = worksheet.cell(row=idx, column=16)  # 第16列是"文件名及链接"
        cell.value = title
        cell.hyperlink = url
        cell.style = 'Hyperlink'

    # 保存Excel
    writer.close()
    print(f"已保存到 {filepath}")

if __name__ == "__main__":
    try:
        print("开始抓取深圳市政策数据...")
        print("需要爬取的两个关键词链接:")
        print("1. 税费减免 - https://sqzc.gd.gov.cn/sqzc/m/cms/policy/getPolicyListPage2?keywords=%E7%A8%8E%E8%B4%B9%E5%87%8F%E5%85%8D")
        print("2. 资金申报 - https://sqzc.gd.gov.cn/sqzc/m/cms/policy/getPolicyListPage2?keywords=%E8%B5%84%E9%87%91%E7%94%B3%E6%8A%A5")
        print(f"搜索关键词: {', '.join(SEARCH_KEYWORDS)}")

        # 获取所有政策数据
        data = fetch_all_policies()

        if not data:
            print("警告：未获取到任何数据！")
        else:
            print(f"\n成功获取 {len(data)} 条数据")

            # 去重处理（基于详情地址）
            seen_urls = set()
            unique_data = []
            for row in data:
                url = row[11]  # 详情地址
                if url not in seen_urls:
                    seen_urls.add(url)
                    unique_data.append(row)

            print(f"去重后剩余 {len(unique_data)} 条数据")

            # 生成简介
            unique_data = generate_summaries(unique_data)

            # 保存数据到Excel
            save_to_excel(unique_data)

    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()