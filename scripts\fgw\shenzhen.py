# 深圳市政策信息抓取脚本
# 需要爬取的两个链接:
# 1. https://sqzc.gd.gov.cn/sqzc/m/cms/policy/getPolicyListPage2?pageNumber=1&pageSize=10&keywords=%E7%A8%8E%E8%B4%B9%E5%87%8F%E5%85%8D&city=%E6%B7%B1%E5%9C%B3&publisher=
# 2. https://sqzc.gd.gov.cn/sqzc/m/cms/policy/getPolicyListPage2?pageNumber=1&pageSize=10&keywords=%E8%B5%84%E9%87%91%E7%94%B3%E6%8A%A5&city=%E6%B7%B1%E5%9C%B3&publisher=
# 详情页: 1、https://sqzc.gd.gov.cn/sqzc/m/cms/publicity/publicityInfo?id={id}
#           2、https://sqzc.gd.gov.cn/sqzc/m/cms/policy/policyinfo/{id}

import re
import requests
from bs4 import BeautifulSoup
import pandas as pd
import datetime
from llm_utils import call_llm
from tqdm import tqdm
import os
import time
import json
import urllib.parse

BASE_URL = "https://sqzc.gd.gov.cn"
LIST_API = f"{BASE_URL}/sqzc/m/cms/policy/getPolicyListPage2"

# 不同关键词对应不同的详情页URL模板
DETAIL_URL_TEMPLATES = {
    "税费减免": f"{BASE_URL}/sqzc/m/cms/publicity/publicityInfo?id={{}}",
    "资金申报": f"{BASE_URL}/sqzc/m/cms/policy/policyinfo/{{}}"
}

headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36",
    "Accept": "application/json, text/plain, */*",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
    "Referer": "https://sqzc.gd.gov.cn/"
}

FIELDS = [
    "来源站点", "部门", "级别", "标题", "简介", "发布时间", "截止时间", "发文机关", "主题", "索引号", "文号", "详情地址", "正文内容", "敏感数字（元，%）", "附件", "文件名及链接"
]

# 需要爬取的两个关键词
SEARCH_KEYWORDS = [
    "税费减免",  # %E7%A8%8E%E8%B4%B9%E5%87%8F%E5%85%8D
    "资金申报"   # %E8%B5%84%E9%87%91%E7%94%B3%E6%8A%A5
]

def fetch_detail(policy_id, keyword, title="", pub_date="", publisher="", debug=False, max_retries=3):
    """获取政策详情页内容"""
    # 根据关键词选择对应的详情页URL模板
    detail_url_template = DETAIL_URL_TEMPLATES.get(keyword, DETAIL_URL_TEMPLATES["资金申报"])
    detail_url = detail_url_template.format(policy_id)

    for attempt in range(max_retries):
        try:
            resp = requests.get(detail_url, headers=headers, timeout=15)
            resp.encoding = 'utf-8'

            if resp.status_code != 200:
                print(f"    警告: HTTP状态码 {resp.status_code}, 尝试 {attempt + 1}/{max_retries}")
                if attempt < max_retries - 1:
                    time.sleep(2)
                    continue
                else:
                    return None

            soup = BeautifulSoup(resp.text, "html.parser")

            if debug:
                print(f"    调试信息 - 关键词: {keyword}")
                print(f"    调试信息 - 详情页URL: {detail_url}")
                print(f"    调试信息 - 响应状态: {resp.status_code}")
                print(f"    调试信息 - 页面长度: {len(resp.text)} 字符")

            # 检查页面是否包含有效内容
            if len(resp.text) < 100:
                print(f"    警告: 页面内容过短，尝试 {attempt + 1}/{max_retries}")
                if attempt < max_retries - 1:
                    time.sleep(2)
                    continue
                else:
                    return None

            # 成功获取页面，跳出重试循环
            break

        except Exception as e:
            print(f"    错误: 获取详情页失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2)
                continue
            else:
                return None

    # 如果所有重试都失败了
    else:
        print(f"    错误: 所有重试都失败，跳过此政策")
        return None

    try:

        # 获取标题
        if not title:
            # 尝试多种标题选择器
            title_selectors = [
                "p.diagramTitle",
                "i#span_title",
                ".diagramTitle",
                "h1",
                "h2"
            ]
            for selector in title_selectors:
                title_elem = soup.select_one(selector)
                if title_elem:
                    title = title_elem.get_text(strip=True)
                    break

        # 获取发布时间
        if not pub_date:
            # 查找包含"发布时间"的span元素
            spans = soup.find_all("span")
            for span in spans:
                span_text = span.get_text(strip=True)
                if "发布时间" in span_text:
                    # 查找span内的i标签
                    i_tag = span.find("i")
                    if i_tag:
                        pub_date = i_tag.get_text(strip=True)
                        break

            # 如果还没找到，尝试从文本中提取
            if not pub_date:
                time_pattern = r'发布时间\s*[：:]\s*(\d{4}-\d{2}-\d{2})'
                time_match = re.search(time_pattern, soup.get_text())
                if time_match:
                    pub_date = time_match.group(1)

        # 获取发布单位
        if not publisher:
            # 查找包含"发布单位"的span元素
            spans = soup.find_all("span")
            for span in spans:
                span_text = span.get_text(strip=True)
                if "发布单位" in span_text:
                    # 查找span内的i标签
                    i_tag = span.find("i")
                    if i_tag:
                        publisher = i_tag.get_text(strip=True)
                        break

        # 获取正文内容 - 直接从 div.articleContent 提取
        content = ""
        content_div = soup.find("div", class_="articleContent")
        if content_div:
            content = content_div.get_text("\n", strip=True)

        if debug and content:
            print(f"    调试信息 - 正文内容前100字符: {content[:100]}...")

        # 获取文号 - 从正文内容中提取，使用 深〔〕号 格式
        doc_no = ""
        if content:
            # 使用简单的正则表达式匹配 深〔〕号 格式
            doc_no_match = re.search(r'深[^〔]*〔\d{4}〕\d+号', content)
            if doc_no_match:
                doc_no = doc_no_match.group(0)

        if debug and doc_no:
            print(f"    调试信息 - 提取到文号: {doc_no}")

        # 敏感数字（元，%）- 从正文内容中提取
        sensitive = ""
        if content:
            # 提取包含数字和单位的敏感信息
            sensitive_patterns = re.findall(r'\d+(?:\.\d+)?(?:万|千万|亿)?(?:元|%|％)', content)
            sensitive = ", ".join(sensitive_patterns)

        # 获取附件信息
        attachment = ""
        # 尝试多种附件选择器
        attachment_selectors = [
            "div.relatedAccessory",
            "div.relatedAccessory.fileInfos.fileUrlList",
            ".relatedAccessory"
        ]

        for selector in attachment_selectors:
            attachment_div = soup.select_one(selector)
            if attachment_div:
                # 检查是否是空的附件区域
                div_classes = attachment_div.get("class", [])
                if "none" in div_classes:
                    continue

                # 查找附件链接
                attachment_links = attachment_div.find_all("a", href=True)
                for a in attachment_links:
                    href = a.get("href")
                    if href:
                        full_href = href if href.startswith("http") else BASE_URL + href
                        attachment_text = a.get_text(strip=True)
                        attachment += f"{attachment_text}: {full_href}, "

                # 如果找到附件就跳出循环
                if attachment_links:
                    break

        # 如果没有找到附件链接，尝试查找附件文本信息
        if not attachment:
            attachment_texts = soup.find_all("p", class_="accessoryText")
            for p in attachment_texts:
                attachment_link = p.find("a", href=True)
                if attachment_link:
                    href = attachment_link.get("href")
                    full_href = href if href.startswith("http") else BASE_URL + href
                    attachment_text = attachment_link.get_text(strip=True)
                    attachment += f"{attachment_text}: {full_href}, "

        # 获取索引号（如果有的话）
        index_no = ""
        index_patterns = [
            r'索引号[：:]\s*([A-Za-z0-9\-]+)',
            r'文件编号[：:]\s*([A-Za-z0-9\-]+)',
            r'编号[：:]\s*([A-Za-z0-9\-]+)'
        ]
        for pattern in index_patterns:
            index_match = re.search(pattern, content)
            if index_match:
                index_no = index_match.group(1)
                break

        # 获取截止时间（如果有的话）
        deadline = ""
        deadline_patterns = [
            r'截止时间[：:]\s*(\d{4}-\d{2}-\d{2})',
            r'申报时间[：:]\s*(\d{4}年\d{1,2}月\d{1,2}日至\d{4}年\d{1,2}月\d{1,2}日)',
            r'受理时间[：:]\s*(\d{4}年\d{1,2}月\d{1,2}日至\d{4}年\d{1,2}月\d{1,2}日)'
        ]
        for pattern in deadline_patterns:
            deadline_match = re.search(pattern, content)
            if deadline_match:
                deadline = deadline_match.group(1)
                break

        # 发文机关
        issuing_agency = publisher

        # 文件名及链接
        file_link = title

        # 组装数据行
        row = [
            "深圳市政策平台",        # 来源站点
            "深圳市政府",           # 部门
            "深圳",                # 级别
            title,                # 标题
            "",                   # 简介（后面用大模型生成）
            pub_date,             # 发布时间
            deadline,             # 截止时间
            issuing_agency,       # 发文机关
            "",                   # 主题（在fetch_all_policies中会设置为关键词）
            index_no,             # 索引号
            doc_no,               # 文号
            detail_url,           # 详情地址
            content,              # 正文内容
            sensitive,            # 敏感数字
            attachment.rstrip(", "), # 附件
            file_link             # 文件名及链接
        ]

        if debug:
            print(f"    调试信息 - 提取结果:")
            print(f"      标题: {title[:50]}...")
            print(f"      发布时间: {pub_date}")
            print(f"      发布单位: {issuing_agency}")
            print(f"      文号: {doc_no}")
            print(f"      索引号: {index_no}")
            print(f"      截止时间: {deadline}")
            print(f"      附件数量: {len(attachment.split(',')) if attachment else 0}")
            print(f"      正文长度: {len(content)} 字符")
            print(f"      敏感数字: {sensitive[:100]}..." if sensitive else "      敏感数字: 无")

            # 检查关键字段是否为空
            missing_fields = []
            if not title: missing_fields.append("标题")
            if not pub_date: missing_fields.append("发布时间")
            if not issuing_agency: missing_fields.append("发布单位")
            if not content: missing_fields.append("正文内容")
            if not doc_no: missing_fields.append("文号")

            if missing_fields:
                print(f"    ⚠️ 警告 - 缺失字段: {', '.join(missing_fields)}")
            else:
                print(f"    ✅ 所有关键字段提取成功")

        return row

    except Exception as e:
        print(f"    错误: 解析详情页失败: {e}")
        return None

def fetch_policy_list(keyword, page_number=1, page_size=10):
    """获取政策列表"""
    params = {
        "pageNumber": page_number,
        "pageSize": page_size,
        "keywords": keyword,
        "city": "深圳",
        "publisher": ""
    }

    try:
        resp = requests.get(LIST_API, params=params, headers=headers, timeout=10)
        resp.encoding = 'utf-8'
        data = resp.json()

        if data.get("success") and data.get("data"):
            return data
        else:
            print(f"API返回错误: {data.get('msg', '未知错误')}")
            return None

    except Exception as e:
        print(f"获取政策列表失败: {e}")
        return None

def fetch_all_policies():
    """获取两个关键词的所有政策数据"""
    all_data = []

    for keyword in SEARCH_KEYWORDS:
        print(f"\n正在搜索关键词: {keyword}")
        page = 1

        while True:
            print(f"  正在获取第 {page} 页...")
            result = fetch_policy_list(keyword, page)

            if not result or not result.get("data"):
                print(f"  关键词 '{keyword}' 第 {page} 页无数据，结束搜索")
                break

            policies = result["data"]
            total = result.get("total", 0)
            page_size = result.get("pageSize", 10)

            print(f"  第 {page} 页获取到 {len(policies)} 条政策，总计 {total} 条")

            # 处理每个政策
            for i, policy in enumerate(policies, 1):
                policy_id = policy.get("id")
                title = policy.get("title", "")
                pub_date = policy.get("publishDate", "")
                publisher = policy.get("publisher", "")

                if policy_id:
                    print(f"    [{i}/{len(policies)}] 正在获取详情: {title[:50]}...")
                    # 对第一个政策启用调试模式，查看提取效果
                    debug_mode = (len(all_data) == 0)
                    row = fetch_detail(policy_id, keyword, title, pub_date, publisher, debug=debug_mode)
                    if row:
                        # 在数据中标记关键词来源
                        row[8] = keyword  # 主题字段用来标记关键词
                        all_data.append(row)

                    # 添加延迟，避免请求过快
                    time.sleep(1)

            # 检查是否还有下一页
            if len(policies) < page_size:
                print(f"  关键词 '{keyword}' 已获取完所有数据")
                break

            page += 1

            # 限制最大页数，避免无限循环（可根据需要调整）
            if page > 1:
                print(f"  关键词 '{keyword}' 已达到最大页数限制 (1页)")
                break

    return all_data

# 移除复杂的测试函数，保持代码简洁

def check_data_completeness(data):
    """检查数据完整性"""
    print("\n=== 数据完整性检查 ===")

    if not data:
        print("❌ 没有数据")
        return

    total_count = len(data)
    print(f"📊 总数据量: {total_count} 条")

    # 检查各字段的完整性
    field_stats = {}
    for i, field_name in enumerate(FIELDS):
        non_empty_count = sum(1 for row in data if row[i] and str(row[i]).strip())
        field_stats[field_name] = {
            'count': non_empty_count,
            'percentage': (non_empty_count / total_count) * 100
        }

    print("\n📋 字段完整性统计:")
    for field_name, stats in field_stats.items():
        status = "✅" if stats['percentage'] > 80 else "⚠️" if stats['percentage'] > 50 else "❌"
        print(f"  {status} {field_name}: {stats['count']}/{total_count} ({stats['percentage']:.1f}%)")

    # 检查关键字段
    critical_fields = ["标题", "发布时间", "正文内容", "详情地址"]
    print(f"\n🔍 关键字段检查:")
    for field_name in critical_fields:
        if field_name in field_stats:
            stats = field_stats[field_name]
            if stats['percentage'] < 90:
                print(f"  ⚠️ {field_name} 完整性较低: {stats['percentage']:.1f}%")
            else:
                print(f"  ✅ {field_name} 完整性良好: {stats['percentage']:.1f}%")

    # 按关键词统计
    keyword_stats = {}
    for row in data:
        keyword = row[8]  # 主题字段
        if keyword not in keyword_stats:
            keyword_stats[keyword] = 0
        keyword_stats[keyword] += 1

    print(f"\n📈 按关键词统计:")
    for keyword, count in keyword_stats.items():
        print(f"  📌 {keyword}: {count} 条")

    print("=" * 50)

def generate_summaries(data):
    """为每条数据生成简介"""
    print("\n正在为每条数据生成简介（50字以内摘要）...")
    for row in tqdm(data, desc="生成简介", ncols=80):
        content = row[12]  # 正文内容
        if content:
            try:
                prompt = "请为以下文章内容生成一个50字以内的简要摘要，不要超过50字：\n\n" + content
                summary = call_llm(prompt)
                # 确保摘要不超过50字
                if len(summary) > 50:
                    summary = summary[:47] + "..."
                row[4] = summary  # 简介列
            except Exception as e:
                print(f"生成简介失败: {e}")
    return data

def save_to_excel(data):
    """保存数据到Excel"""
    if not data:
        print("没有数据可保存")
        return

    # 创建数据目录
    data_dir = os.path.join(os.path.dirname(__file__), "data")
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)

    # 生成文件名
    now = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    filename = f"深圳市政策-税费减免+资金申报-{now}.xlsx"

    # 完整文件路径
    filepath = os.path.join(data_dir, filename)

    # 创建DataFrame
    df = pd.DataFrame(data, columns=FIELDS)

    # 创建一个Excel writer对象
    writer = pd.ExcelWriter(filepath, engine='openpyxl')

    # 将DataFrame写入Excel
    df.to_excel(writer, index=False)

    # 获取工作表
    worksheet = writer.sheets['Sheet1']

    # 为"文件名及链接"列添加超链接
    for idx, row in enumerate(data, start=2):  # Excel行从2开始（1是标题行）
        title = row[3]  # 标题
        url = row[11]   # 详情地址
        cell = worksheet.cell(row=idx, column=16)  # 第16列是"文件名及链接"
        cell.value = title
        cell.hyperlink = url
        cell.style = 'Hyperlink'

    # 保存Excel
    writer.close()
    print(f"已保存到 {filepath}")

if __name__ == "__main__":
    try:
        print("开始抓取深圳市政策数据...")
        print("需要爬取的两个关键词链接:")
        print("1. 税费减免 - https://sqzc.gd.gov.cn/sqzc/m/cms/policy/getPolicyListPage2?keywords=%E7%A8%8E%E8%B4%B9%E5%87%8F%E5%85%8D")
        print("2. 资金申报 - https://sqzc.gd.gov.cn/sqzc/m/cms/policy/getPolicyListPage2?keywords=%E8%B5%84%E9%87%91%E7%94%B3%E6%8A%A5")
        print(f"搜索关键词: {', '.join(SEARCH_KEYWORDS)}")

        # 获取所有政策数据
        data = fetch_all_policies()

        if not data:
            print("警告：未获取到任何数据！")
        else:
            print(f"\n成功获取 {len(data)} 条数据")

            # 去重处理（基于详情地址）
            seen_urls = set()
            unique_data = []
            for row in data:
                url = row[11]  # 详情地址
                if url not in seen_urls:
                    seen_urls.add(url)
                    unique_data.append(row)

            print(f"去重后剩余 {len(unique_data)} 条数据")

            # 检查数据完整性
            check_data_completeness(unique_data)

            # 生成简介
            unique_data = generate_summaries(unique_data)

            # 保存数据到Excel
            save_to_excel(unique_data)

    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()