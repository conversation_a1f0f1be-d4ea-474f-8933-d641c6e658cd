<!DOCTYPE html>

<html>

<head>

	<meta name="SiteName" content="广东涉企政策发布平台" >

	<meta name="SiteDomain" content="sqzc.gd.gov.cn" >

	<meta name="SiteIDCode" content="4400000131" >

	<meta name="ColumnName" content="查结果" >

	<meta name="ColumnDescription" content="广东省涉企政策发布平台查结果栏目发布广东省和各地市有关涉企法规和政策执行结果信息等，包括公示、公告等。提供以指定条件和任意关键词检索相关公示公告的查询功能。" >

	<meta name="ColumnKeywords" content="广东省文件,政策文件,涉企文件,涉企公示" >

	<meta name="ColumnType" content="查结果" >

	<meta name="ArticleTitle" content='深圳市财政局 深圳市人力资源和社会保障局 深圳市科技创新局 国家税务总局深圳市税务局关于印发《深圳市粤港澳大湾区个人所得税优惠政策2024纳税年度财政补贴申报指南》的通知' >

	<meta name="PubDate" content='2025-05-30 17:16' >

	<meta name="ContentSource" content='深圳市财政局' >

<title>深圳市财政局 深圳市人力资源和社会保障局 深圳市科技创新局 国家税务总局深圳市税务局关于印发《深圳市粤港澳大湾区个人所得税优惠政策2024纳税年度财政补贴申报指南》的通知</title>

	<meta http-equiv="Content-Type" content="text/html; charset=utf-8">

	<meta http-equiv="X-UA-Compatible" content="IE=edge">

	<meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" />

	<meta name="apple-mobile-web-app-capable" content="yes">

	<meta name="apple-mobile-web-app-status-bar-style" content="black">

	<meta name="format-detection" content="telephone=no">

	<meta data-n-head="true" data-hid="description" name="description" content="广东省涉企政策发布平台 - 发布广东省和各地市制定的涉企法规和政策文件、相关的解读以及各类政策专题。提供以关键词检索相关政策、解读、项目和公示的查询功能。"/>

	<meta data-n-head="true" data-hid="keyword" name="keyword" content="广东涉企政策发布平台,涉企政策一站式服务,粤企政策通,涉企政策"/>

	<meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">



<link rel="stylesheet" type="text/css" href="/sqzc/js/layui/css/layui.css?v=20211025"  media="all" />



<link rel="stylesheet" type="text/css" href="/sqzc/css/common.css?v=20211025"  />

<link rel="stylesheet" type="text/css" href="/sqzc/css/style.css?v=20211025"  	/>

<link rel="stylesheet" type="text/css" href="/sqzc/css/cover.css?v=20211025&t=1.00" />

<link rel="stylesheet" type="text/css" href="/sqzc/css/fonts/iconfont.css?v=20211025&t=1.00"  />

<link rel="shortcut icon" type="image/png" href="/sqzc/images/logo.png" sizes="16x16">

<script src="/sqzc/js/jquery/jQuery-2.1.3.min.js" type="text/javascript" charset="utf-8"></script>

	<style type="text/css">

		.diagramLeft .eventList-head

		{

			margin: 0px 10px 0px 10px;

		}

    </style>

	<link rel="stylesheet"  type="text/css" href="/sqzc/css/policyinfocss.css?v=20211025">

</head>

<body class="gvmHomeIndex">

<!--头部-->

<script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.3.2.js"></script>

<script>

	wx.miniProgram.navigateBack({url: '/pages/home/<USER>'})

	wx.miniProgram.postMessage({ data: {msg: '' }});

</script>

<script type="text/javascript" src="https://cdn.staticfile.org/jquery-cookie/1.4.1/jquery.cookie.min.js"></script>

<!-- <script type="text/javascript" src="//api.map.baidu.com/getscript?v=3.0&ak=L6wio39Dee0SixbufOFPnMCjTvTicZMP&s=1"></script> -->



<style>

.newPhoneBg{

	background: url('/sqzc/images/newHomeImg/bj-360.png') no-repeat center top;

}

/*热搜词-开始*/

.hotwords {

	width: 492px;

}

.hotwords div {

	float: left;

	display: inline-block;

	font-size: 14px;

	color: white;

	text-shadow: 3px 3px 12px black;

}

.hotwords div:last-child {

    width: 85%;

}

.hotwords span {

	/*  padding-right: 10px; */

	 cursor: pointer;

	 padding:0px 5px 0px 5px;

}

.hotwords span:hover {

	 /* color: #3E97DF; */

     /* text-decoration: underline; */

     background-color:#688fae;

}

/*热搜词-结束*/

</style>

<div class='gvmHeader zcjdHeaher '>

    

    

    

   

        <!--头部logo栏-->

            	<!--头部logo栏-->

        <div class="header-title">

	        <div class="header-title-cont">

	            <div class="platformName">

	            	<a href="http://www.gd.gov.cn/" target="_blank">

	            		<img src="/sqzc/images/newHomeImg/国徽1024.png" style="margin-top: 18px;width:40px;position: absolute;"/>

	                    <img src="/sqzc/images/newHomeImg/<EMAIL>" style="margin-top: -10px;width:180px;"/>

	                </a>

	                <a href="/sqzc/m/home/<USER>">

	                    <!-- <img src="/sqzc/images/newHomeImg/newLogo-2.png" alt="" class="logoImg"> -->

	                    <img src="/sqzc/images/newHomeImg/logo.png" style="margin-top: -10px;"/>

	                    		<span style="display: inline-block;width:135px;">粤企政策通</span>

	                    <i class="middleLine" style="height:20px;top:0px;"></i>

	                </a>

		                <a href="javascript:void(0);"><span class="zwfwText">广东省</span></a>

	                	<div id="changeCity">

		                	<span>切换地区<img src="/sqzc/images/jt1.png"/></span>

	                	</div>

	            </div>

	            <form action="" class="layui-form " onsubmit="return false;">

	                    <input type="text" placeholder="" class="layui-input searchInput" id="searchButton"

	                onkeyup="common.onKeyEnter(event,function(){toCmsSearchList2();});">

	                <span class="iconfont searchIcon" onclick="toCmsSearchList2();">&#xe606;</span>

	            </form>

	                   <div class="header-title-login">       

            <span class="LoginType">

                    <i class="qywxLogin">

						<img src="/sqzc/images/qywx.png" alt="">

						<div class="appCodeDiv none">

	                   		<img class="appCode" src="/sqzc/images/qywxapp.jpg" />

	                   		<img class="codePng" src="/sqzc/images/qr.png">

	                   		<span style="font-size:15px">粤企政策通-企业微信</span>

                   		</div>

					</i>                   

                   

                   	<i class="wechatLogin">

                   		<img src="/sqzc/images/wx.png" alt="">

                   		<div class="appCodeDiv none">

	                   		<img class="appCode" src="/sqzc/images/wxapp.jpg" />

	                   		<img class="codePng" src="/sqzc/images/qr.png">

	                   		<span>粤企政策通-小程序</span>

                   		</div>

                   	</i>  

                     <i class="middleLine"></i>

                </span>

                

                <div class="loginText">

                       <span class="loginBefore"  onclick="login();">登录</span>

                </div>

            </div> 

            



		        <div class="cityBox none">

	            	<div style="cursor: pointer;"class="city canUsed">广东省 (全省政策)</div>

	            	<ul>

	            		<li class="city canUsed">广州市</li>

	            		<li class="city canUsed">深圳市</li>

	            		<li class="city canUsed">珠海市</li>

	            		<li class="city canUsed">汕头市</li>

	            		<li class="city canUsed">佛山市</li>

	            		<li class="city canUsed">韶关市</li>

	            		<li class="city canUsed">河源市</li>

	            		<li class="city canUsed">梅州市</li>

	            		<li class="city canUsed">惠州市</li>

	            		<li class="city canUsed">汕尾市</li>

	            		<li class="city canUsed">东莞市</li>

	            		<li class="city canUsed">中山市</li>

	            		<li class="city canUsed">江门市</li>

	            		<li class="city canUsed">阳江市</li>

	            		<li class="city canUsed">湛江市</li>

	            		<li class="city canUsed">茂名市</li>

	            		<li class="city canUsed">肇庆市</li>

	            		<li class="city canUsed">清远市</li>

	            		<li class="city canUsed">潮州市</li>

	            		<li class="city canUsed">揭阳市</li>

	            		<li class="city canUsed">云浮市</li>

	            	</ul>

	            </div>

	        </div>

	        

	    </div>

        <!--头部logo栏-->



        <!--头部logo栏-->

        

				    <!-- <div class="headLinkTab notIndexHead pcHeadForm">

		        <ul id="_ul_nav">

		            

		        </ul>

		    </div> -->

		    <form action="" class="layui-form newPhoneHead" onsubmit="return false;">

              		<div class="web-header phoneHeadForm">

					    <div class="logo"><img src="/sqzc/css_app/image/bj.png" alt=""/> </div>

					    <div class="caozuo">

					    	<a href="http://www.gd.gov.cn/" target="_blank" style="display: inline-block;padding-top: 20px;padding-right: 20px;">

					    		<img src="/sqzc/images/newHomeImg/国徽.png" style="margin-top: -11px;margin-left: -2px;width:28px;position: absolute;"/>

			                    <img src="/sqzc/images/newHomeImg/<EMAIL>" style="margin-top: -10px;width:120px;"/>

			                </a> 

					    	<a class="xuanze-qy layui-icon layui-icon-left" href="javascript:history.go(-1)"></a> 

					    	<div class="user">

			                       <span class="loginBefore"  onclick="login();">登录</span>

					    	</div> 

				    	</div>

					    <div class="suosou">

					      <input type="text" class="searchInput layui-input"  id="searchText" value="" name="title" lay-verify="title" autocomplete="off" placeholder="请输入关键词" class="layui-input">

					      <i class="iconfont icon-fangdajing1" onclick="toCmsSearchList()"></i> 

					        <div class="phoneHotwords" style="display: none;"></div>

				        </div>

				  </div>

				  <div class="nav headLinkTab notIndexHead">

				    <ul class="navfouce" id="_ul_nav">

				      	<li><a href="/sqzc/m/home/<USER>">首页<i class="linkTabLine"></i></a></li>

			            <li><a href="/sqzc/m/cms/policy" >政策库 <i class="linkTabLine"></i></a></li>

			            <li><a href="/sqzc/m/cms/policyanalysis" >解读库 <i class="linkTabLine"></i></a></li>

			            <li><a href="/sqzc/m/cms/project" >报项目<i class="linkTabLine"></i></a></li>

			            <li><a href="/sqzc/m/cms/publicity" >查结果<i class="linkTabLine"></i></a></li>

			            <li><a href="/sqzc/m/cms/match" >精准搜<i class="linkTabLine"></i></a></li>

			            <!-- <li><a href="/sqzc/m/cms/solicitnouns/consultAndSolicitnouns" >互动交流<i class="linkTabLine"></i></a></li>

			            <li><a href="/sqzc/m/cms/solicitnouns" >意见征求<i class="linkTabLine"></i></a></li> -->

			            <li class="jiaoliuTab"><a href="javascript:void(0);" onclick="toConsultAndSolicitnouns();">互动交流<i class="linkTabLine"></i> </a></li>

			            <li class="redianTab"><a href="http://sqzc.gd.gov.cn/rdzt">热点专题</a></li>

				    </ul>

				  </div>

 			<!--头部tab导航栏-->

        </form>

		    <!--头部搜索栏-->



    

    

    

</div>





<script type="text/javascript">

	

  /* 

  该段代码在common.js文件中，用于判断用户是否已经登录，登录后需重新刷新页面

  $(function(){

	  

	  var userCode=GetUrlParam("code") || "";

	  var isReload = GetUrlParam("isReload") || "";

	  //alert(userCode+"-"+isReload);

	  if(userCode!="" && isReload!="1")

	  {

		  //alert(window.location.href);

		  window.location.href= window.location.href+"&isReload=1";  

	  }

	  

  }); */

  $(function(){

	  //判断是哪个菜单

	  var currUrl = window.location.pathname;  //只获取前面部分，不包括http

	  /* if(currUrl.indexOf("project")>-1){//项目申报与信息公示合并

		  currUrl = currUrl.replace("project","publicity");

	  } */

	  var lis = $("#_ul_nav li");

	  var li = null

	  var url = null;

	  for(var i=0;i<lis.length;i++)

	  {

		  li = $(lis[i]);

		  url = li.find("a").attr("href");	

		  click=li.find("a").attr("onclick");

		  if(currUrl==url || currUrl.indexOf(url+"/")>-1||typeof(click)!="undefined") //存在，加上斜杆防止类似

		  {

			  li.addClass("presentPage activeLi");

			  break;  //终止循环

		  }

	  }

	  

  });

  

  //跳转到查询列表

  function toCmsSearchList()

  {

	var searchText = $("#searchText").val();

	var url = '/sqzc/m/cms/search?searchText='+searchText;

	url = encodeURI(encodeURI(url.replace(/%/g,'%25')));

	if(browserRedirect()){

		window.location.href = url;

	}else{

		common.win_open(url);

	}

	  

  }

  

  //跳转到查询列表

  function toCmsSearchList2()

  {

	var searchText = $("#searchButton").val();

	var url = '/sqzc/m/cms/search?searchText='+searchText;

	url = encodeURI(encodeURI(url.replace(/%/g,'%25')));

	if(browserRedirect()){

		window.location.href = url;

	}else{

		common.win_open(url);

	}

	  

  }

  

</script>

<!--广东省切换-->

<div class="dibutangchu">

    <div class="aui-popup aui-popup-bottom" id="bottom">

        <div class="tankuan-box">

            <div class="aui-tab" id="tab2">

                <div class="aui-tab-item aui-active">区域</div>

            </div>

            <div id="tab2-con">

                <div id="tab2-con1" class="tc-btn">

                    <ul>

                        <li><a href="void:()" onclick="return false;" class="btn-active">广东省 (全省政策)</a></li>

                        <li><a href="void:()" onclick="return false;">广州市</a></li>

	            		<li><a href="void:()" onclick="return false;">深圳市</a></li>

	            		<li><a href="void:()" onclick="return false;">珠海市</a></li>

	            		<li><a href="void:()" onclick="return false;">汕头市</a></li>

	            		<li><a href="void:()" onclick="return false;">佛山市</a></li>

	            		<li><a href="void:()" onclick="return false;">韶关市</a></li>

	            		<li><a href="void:()" onclick="return false;">河源市</a></li>

	            		<li><a href="void:()" onclick="return false;">梅州市</a></li>

	            		<li><a href="void:()" onclick="return false;">惠州市</a></li>

	            		<li><a href="void:()" onclick="return false;">汕尾市</a></li>

	            		<li><a href="void:()" onclick="return false;">东莞市</a></li>

	            		<li><a href="void:()" onclick="return false;">中山市</a></li>

	            		<li><a href="void:()" onclick="return false;">江门市</a></li>

	            		<li><a href="void:()" onclick="return false;">阳江市</a></li>

	            		<li><a href="void:()" onclick="return false;">湛江市</a></li>

	            		<li><a href="void:()" onclick="return false;">茂名市</a></li>

	            		<li><a href="void:()" onclick="return false;">肇庆市</a></li>

	            		<li><a href="void:()" onclick="return false;">清远市</a></li>

	            		<li><a href="void:()" onclick="return false;">潮州市</a></li>

	            		<li><a href="void:()" onclick="return false;">揭阳市</a></li>

	            		<li><a href="void:()" onclick="return false;">云浮市</a></li>

                    </ul>

                </div>

                <!--<div id="tab2-con2" class="aui-hide"></div>

                <div id="tab2-con3" class="aui-hide"></div>-->

            </div>

        </div>

    </div>

    <!--广东省切换-->

</div>



<script type="text/javascript">



function login(){

	//登陆

	//function(url, data, dataType, callback,ecallback,ajaxOptions);

	var headurl="https://tyrz.gdbs.gov.cn/am/login/initAuth.do?gotoUrl=";

	var gotourl="https://tyrz.gd.gov.cn/tif/sso/connect/page/oauth2/authorize?service=initService"

			+"&response_type=code&scope=all"

			+"&client_id=gdbssqzc&client_secret=sq20190130"

			+"&redirect_uri=";

	var redirecturl="https://sqzc.gd.gov.cn/sqzc/m/home/<USER>"; //需要修改为返回的地址

	//var redirecturl = "http://127.0.0.1:8080/index"; //测试用

	//var selfurl=headurl+encodeURIComponent(gotourl+encodeURIComponent(redirecturl));

	var selfurl=gotourl+encodeURIComponent(redirecturl);

	location.href=selfurl;

	//window.open(selfurl);

	

    /* common.ajaxFunc("/sqzc/m/pf/login/userLogin",{},"json",function(result){

    	if(result.success){

    		location.reload();

    	}

    }); */

}



function logout()

{

	layui.use(['layer'], function(){

		//注销

		layer.confirm('确定退出？', {

			  btn: ['确定','取消'] //按钮

			}, function(){

			  

				common.ajaxFunc("/sqzc/m/pf/login/logout",{},"json",function(result){

					if(result.success){

						//location.reload();

						

						//window.location.href="/sqzc/m/home/<USER>";

						window.location.href="http://tyrz.gd.gov.cn/_tif_sso_logout_/?redirect_uri=https%3A%2F%2Fsqzc.gd.gov.cn%2Fsqzc%2Fm%2Fhome%2Findex";

					}

				});  

				

			}, function(){  //取消

			  

		});

		

	});

	

	

	

}

//分站访问+1

function addChildWebReadCount(webCity){

	var city = webCity;

	var params = {};

	var appCode = "pc";

	if(browserRedirect()){

		appCode = "mini";

	}

	console.log(appCode);

	if(city){

		params = {'city':city,'appCode':appCode};

	}else{

		params = {'city':'广东省','appCode':appCode};

	}

	var url = "/sqzc/m/cms/common/accessCount";

	common.ajaxFunc(url,params,"json",function(result){

		//console.log(result);

		if (result.success) {

			//console.log(result.data);

			getVistiCount();

		}

	});

}

//获取城市

function getHeadCity() {

	var url = '/sqzc/m/pf/city/getList';

	var data = '';

	common.ajaxFunc(url,data,'json',function(result){

		if (result.success) {

			var list = result.data[0];

			////console.log(list);

			var html = "";

			for(var i=0;i<list.length;i++){

				var city = list[i].city_name;

				var open_status = list[i].open_status;

				if(city == "广东省"){

					continue;

				}

				if(open_status==1){

					/* $(".city").each(function(n,item){

						if($(item).text()==city){

							$(item).addClass("canUsed");

						}

					}) */

					html += '<li class="city canUsed">'+city+'</li>';

				}else{

					html += '<li class="city">'+city+'</li>';

				}

			}

			$(".cityBox ul").empty();

			$(".cityBox ul").html(html);

		}

	});

}

function getCityCookie(name){

	/* var value = unescape($.cookie(name)?$.cookie(name):""); */

	var value = getCookie("cityRecord");

	return value;

}

//登陆后个人中心、工作台跳转

function personalCenter(url)

{

	if(browserRedirect()){

		  window.location.href = url;

	}else{

		 window.open(url);

	}

}

//判断pc端还是手机端

var isPhone = false;

if(browserRedirect()){

	  $('.phoneHeadForm').show();

	  $('.pcHeadForm,.header-title-cont,.service').hide();

	  $('.newPhoneHead').addClass('newPhoneBg');

}else{

	  $('.phoneHeadForm,.dibutangchu').remove();

	  $('.pcHeadForm,.header-title-cont,.service').show();

}

var region = "广东省";

function getlocationByAPI(flag){

	//怀疑下面的百度API会导致首页加载缓慢，因此先注释掉

	//var map = new BMap.Map("container");

    //var nowCity = new BMap.LocalCity();

    //nowCity.get(bdGetPosition);

    

    //怀疑下面的百度API会导致首页加载缓慢，因此先注释掉，将定位默认为广州市

    setCookie("visitPlace",decodeURI('广州市','utf-8'));

    if(!hadCount){

    	counter(region);//访问量、访问人次统计

    	hadCount = true;

    }

    if(flag){

    	afterChangeWebCity();

    }

    

    function bdGetPosition(result){

    	console.log("result="+result);

		// if(result.name != null &&)

		//console.log("result.name"=+);

    	var cityName = result.name; //当前的城市名

		var lat;

		var lng;

		try {

			lat = result.center.lat;

    		lng = result.center.lng;

		} catch (error) {

			lat = 23.13533631;

			lng = 113.27143134;

		}



    	//按经纬度获取省份

   //  	$.ajax({

   //          type: "POST",

   //          dataType: "jsonp",

   //          url: '//api.map.baidu.com/reverse_geocoding/v3/?ak=L6wio39Dee0SixbufOFPnMCjTvTicZMP&callback=renderReverse&location=' + lat + ',' + lng + '&output=json',

   //          success: function (response) {

   //          	//console.log(response);

   //              if (response.status == 0) {

   //                  var province = response.result.addressComponent.province;

   //                  if(province == '广东省'){

   //                  	//region = cityName;

                    	

	  //                  	 var nowTime = new Date().getTime();

	  //                  	 var positionTime = getCookie("positionTime");

	  //                  	var position = getCookie("sq_position");

	  //                  	 //定位有效设置1天

	  //                  	 var effectiveTime = 24 * 60 * 60 * 1000;//(24 * 60 * 60 * 1000)

	  //                  	 //console.log(nowTime - positionTime);

	  //                  	setCookie("visitPlace",decodeURI(cityName,'utf-8'));

	  //                  	 /* if(position != 'used' || nowTime - positionTime > effectiveTime){

	  //                  		setCookie("sq_position","used");

   //                     	 	setCookie("positionTime",nowTime);

	  //                      	setCookie("webCity",decodeURI(region));

   //         					$.cookie("cityRecord",escape(region),{ expires: 7 });

	  //                  	 }else if(position == 'used' && nowTime - positionTime < effectiveTime){

	  //                  		 console.log('1日内已定位过，现以webCity设置子站');

	  //                   	//setCookie("positionTime",nowTime);

	  //                   	region = getCookie("webCity");

	  //                   	if(region && region.indexOf('%u') != -1){

	  //                   		region = unescape(region);

		 //                   	 }

		 //                   	 if(region && region!=""){

		                   		  

	  //                        }else{

	  //                       	 region = getCityCookie("cityRecord");

	  //                      	 	if(region && region.indexOf('%u') != -1){

	  //                      	 	region = unescape(region);

	  //                      	 	}

	  //                      	  	setCookie("webCity",region);

	  //                        }

	  //                  	 } */

	  //                  	 //console.log(region);

   //                  }

   //             	    if(!hadCount){

   //              		counter(region);//访问量、访问人次统计

   //              		hadCount = true;

   //             	    }

	  //              	 /* var zwfwText= $('.zwfwText').text();

	  //              	 if (zwfwText=="标签查询"){

	  //              		 $('#changeCityLable').removeClass('none');

	  //              	 }  

   //                	//addChildWebReadCount(webCity);//访问人次+1

   //             	    if (zwfwText!="政策匹配分析" && zwfwText!="工作台" && region){

   //               	  	$('.zwfwText').text(region);

   //               	  	$('.xuanze-qy[tapmode]').text(region);

   //             	    }

   //                 	$('.cityBox .city').each(function(n,item){

   //  					$(item).removeClass("beSelected");

   //                 		if($(item).text() == region){

   //                 			$(item).addClass("beSelected");

   //                 		}

   //  				})

   //     				$('.zwfwText').text(region); */

   //     				if(flag){

   //      				afterChangeWebCity();

   //     				}

   //              }

			// 	else {

			// 		if(!hadCount){

			// 			counter(region);//访问量、访问人次统计

			// 			hadCount = true;

			// 		}

			// 		if(flag){

			// 			afterChangeWebCity();

			// 		}

			// 	}

   //          },

			// error: function (response) {

			// 	console.log("response.status="+response.status);

			// 	if(!hadCount){

			// 		counter(region);//访问量、访问人次统计

			// 		hadCount = true;

			// 	}

			// 	if(flag){

			// 		afterChangeWebCity();

			// 	}

			// }  

   //      });

   }

}



function isNull(obj) {

	if(typeof obj == "undefined" || obj == null || obj == ""){

		return true;

	}

	return false;

}



var hadCount = false;

$(function () {

		getlocationByAPI();

		setCookie("webCity",'');

	  getHotword(); 

	  getHeadCity();

	  

    //模拟点击登录按钮静态操作

    /* $('.loginBefore').click(function () {

    	var tal ='<i class="layui-icon">&#xe667;</i><i class="middleLine"></i>';

        $(this).hide().next('.beLogin').show();

        $('.LoginType').html(tal); 

    });*/

    $('.beLogin').click(function () {

    	$(this).next('.personalLink').toggleClass('showBlock');

    }); 

    

    

    $(document).on("click",function(e){

        var e = e || window.event; //浏览器兼容性

        var elem = e.target || e.srcElement;

        while (elem) { //循环判断至跟节点，防止点击的是div子元素

            if (elem.id && elem.id == 'beLogin') {

                return;

            }

            elem = elem.parentNode;

        }

        if ($('.personalLink').hasClass('showBlock')){

            $('.personalLink').removeClass('showBlock');

        }

    });

    //            模拟点击登录按钮静态操作----end



    //            模拟点击退出登录静态操作

    $('.logout').click(function () {

        var tal ='<i class="qqLogin"></i> <i class="wechatLogin"></i> <i class="middleLine"></i>';

        $('.loginBefore').show().next('.beLogin').hide();

        $('.LoginType').html(tal);

    });

	$('#changeCity').click(function(){

		$('.cityBox').toggleClass('none');

		if ($('.cityBox').hasClass('none')){

			$('#changeCity').html('<span>切换地区<img src="/sqzc/images/jt1.png"/></span>');

		}else{

			$('#changeCity').html('<span>切换地区<img src="/sqzc/images/jt2.png"/></span>');

		}

	});

	//切换分站(地区)

	$('.cityBox').on('click','.city',function(){

		$('.cityBox').addClass('none');

		var city = $(this).text();

		if(city && city.indexOf('广东省') != -1){

			city = '广东省';

		}

		if($(this).hasClass("canUsed")){

			var oldText = $('.zwfwText').text();

			if(city != oldText){

				counter(city);

				//addChildWebReadCount(city);

				$('.cityBox .city').each(function(n,item){

					$(item).removeClass("beSelected");

				})

				$(this).addClass("beSelected");

				$('.zwfwText').text(city);

				//console.log(city)

				setCookie("webCity",decodeURI(city));

				$.cookie("cityRecord",escape(city),{ expires: 7 });

				afterChangeWebCity();

			}

		}

	});

	$('.cityBox .city').each(function(n,item){

		$(item).removeClass("beSelected");

		if($(item).text() == $('.zwfwText').text()){

			$(item).addClass("beSelected");

		}

	})

	$('.tc-btn ul li ').on('click','a',function(){

		$(".tc-btn ul li a").each(function(n,item){

			$(item).removeClass("btn-active");

		})

		var that = $(this);

		that.addClass("btn-active");

		var city = $(this).text();

		var oldText = $('.xuanze-qy').text();

		if(city && city.indexOf('广东省') != -1){

			city = '广东省';

		}

		if(city != oldText){

			counter(city);

			//addChildWebReadCount(city);

			$('.cityBox .city').each(function(n,item){

				$(item).removeClass("beSelected");

			})

			$(this).addClass("beSelected");

			$('.xuanze-qy').text(city);

			//console.log(city)

			setCookie("webCity",decodeURI(city));

			$.cookie("cityRecord",escape(city),{ expires: 7 });

			afterChangeWebCity();

		}

		popup.hide(document.getElementById("bottom"));

	})

	

	$(document).click(function(e){

		var text1 =$(e.target).text();

		if(!$('.cityBox').hasClass('none') && text1 !='切换地区'){

			var text2 =$(e.target).attr('class') +'';

			var text3=$(e.target).parents('div').attr('class')+'';

			if(text2.indexOf('cityBox') < 0 && text3.indexOf('cityBox') < 0){

				$('.cityBox').addClass('none');

				$('#changeCity').html('<span>切换地区<img src="/sqzc/images/jt1.png"/></span>');

			}

		}

		if(!$(e.target).hasClass('wechatLogin') && !$(e.target).parent('i').hasClass('wechatLogin') ){

			$('.wechatLogin .appCodeDiv').addClass('none');

		}

		if(!$(e.target).hasClass('qywxLogin') && !$(e.target).parent('i').hasClass('qywxLogin') ){

			$('.qywxLogin .appCodeDiv').addClass('none');

		}

	})

	$('.wechatLogin').click(function(){

		$('.wechatLogin .appCodeDiv').toggleClass('none');

	})

	$('.qywxLogin').click(function(){

		$('.qywxLogin .appCodeDiv').toggleClass('none');

	})

	

	$('.phoneHeadForm .suosou').hover(function(){

		$('.phoneHotwords').show();

	},function(){

		$('.phoneHotwords').hide();

	})

});

//

function toConsultAndSolicitnouns(){

	/* 

	if(!checkLogin()){

		return;

	} */

	var url = "/sqzc/m/cms/solicitnouns/consultAndSolicitnouns";

	//判断pc端还是手机端

	if(browserRedirect()){

		 $('.jiaoliuTab a').removeAttr('onclick').attr('href',url);

	}else{

		url = encodeURI(encodeURI(url.replace(/%/g,'%25')));

		window.open(url);

	}

	

}

function browserRedirect() {

    var sUserAgent = navigator.userAgent.toLowerCase();

    var bIsIpad = sUserAgent.match(/ipad/i) == "ipad";

    var bIsIphoneOs = sUserAgent.match(/iphone os/i) == "iphone os";

    var bIsMidp = sUserAgent.match(/midp/i) == "midp";

    var bIsUc7 = sUserAgent.match(/rv:*******/i) == "rv:*******";

    var bIsUc = sUserAgent.match(/ucweb/i) == "ucweb";

    var bIsAndroid = sUserAgent.match(/android/i) == "android";

    var bIsCE = sUserAgent.match(/windows ce/i) == "windows ce";

    var bIsWM = sUserAgent.match(/windows mobile/i) == "windows mobile";

    if (bIsIpad || bIsIphoneOs || bIsMidp || bIsUc7 || bIsUc || bIsAndroid || bIsCE || bIsWM) {

    //跳转移动端页面

    	return isPhone = true;

    } else {

    //跳转pc端页面

    	return isPhone = false;

    }

}



function getHotword() {

	var url = '/sqzc/m/cms/common/getTopViewLabelList';

	var data = '';

	common.ajaxFunc(url,data,'json',function(result){

		if (result.success) {

			console.info(result.data);

			if(result.data!=null && result.data.length>0) {

				if(browserRedirect()){

					var html = "<div>热搜词：</div><div>";

					for (var i = 0; i < result.data.length; i++) {

						html += "<span>"+result.data[i].label+"</span>";

					}

					html += "</div>";

					$(".phoneHotwords").empty();

					$(".phoneHotwords").html(html);

					$(".phoneHotwords div:last span").click(function(){

						searchingByHotword(this);

					});

				}else{

					var html = "<div>热搜词：</div><div>";

					for (var i = 0; i < result.data.length; i++) {

						html += "<span>"+result.data[i].label+"</span>";

					}

					html += "</div>";

					

					$(".hotwords").empty();

					$(".hotwords").html(html);

					

					$(".hotwords div:last span").click(function(){

						searchingByHotword(this);

					});

				}

			}

		}

	});

}



//搜索按钮(按热词)

function searchingByHotword(obj){

	var hotword = $(obj).text();

	/* var searchTextObj = $(obj).parents().find("#searchText");

	searchTextObj.val(hotword);

	//alert(searchTextObj.val());

	searchTextObj.next().click(); */

	

	if(hotword == null || hotword.trim() == ""){

		return;

	}

	var url = '/sqzc/m/cms/search?searchText='+hotword;

	url = encodeURI(encodeURI(url.replace(/%/g,'%25')));

	if(browserRedirect()){

		window.location.href = url;

	}else{

		window.open(url);

	}

}

//访问量与访问人次统计

function counter(webCity){

	var appCode = "pc";

	if(browserRedirect()){

		appCode = "mini";

	}

	var url = '/sqzc/m/cms/common/counter';

	common.ajaxFunc(url,{'city':webCity,'appCode':appCode},'json',function(result){

		if (result.success) {

			console.info(result.data);

		}

	});

}

</script>



<!--主体内容-->

<div class="indexHome-cont">

    <!--政策原文-->

    <div class="diagram">

        <div class="diagramLeft publicityLeft">

            <div class="eventList-head">

                <span class="eventListTitle xxgsContNav"><!-- 公示正文 --></span>

                <div class="navRightPart" style="line-height: 55px;">

                    <!-- <span class="noteIcon" onclick="leaveMessage()"><i class="iconfont">&#xe6ff;</i> 留言</span> -->

                    <span class="collectIcon beCollect" onclick="saveOrCancelCollect()">

                        <!-- <i class="iconfont">&#xe6d9;</i>

                        <i class="iconfont beCollectIcon">&#xe711;</i> 收藏 -->

                    </span>

                </div>

            </div>



            <div class="diagramImg zcywCont">

                <div class="articleNav">

                    <p class="diagramTitle">深圳市财政局 深圳市人力资源和社会保障局 深圳市科技创新局 国家税务总局深圳市税务局关于印发《深圳市粤港澳大湾区个人所得税优惠政策2024纳税年度财政补贴申报指南》的通知</p>

                    <div class="articleNavBtm">

                        <span>发布时间 : <i>2025-05-30</i></span>

                        <span>发布单位 : <i>深圳市财政局</i></span>



<div class="shareTo">

             <!--分享弹窗-->

            <div class="shareCheck bdsharebuttonbox" data-tag="share_1">

               <!--  <a class="bds_more" data-cmd="more"></a> -->

                <a class="bds_tsina" data-cmd="tsina"></a>

                <a class="bds_weixin" data-cmd="weixin"></a>

            </div>

            <!--分享弹窗-->

            

</div>

  

<script type="text/javascript" src="/sqzc/js/share.js"></script>

<script>



/**

 * _content,分享内容

 * _url,分享地址

 * _desc,摘要

 * _pic,图片

 */

function shareInit(_content, _url, _desc, _pic){

	if(_url == null ||this_url.trim() ==""){

		_url = window.location.href;

	}

	if(_desc == null || _desc.trim() == ""){

		_desc = _content;

	}

	window._bd_share_config = {

	        common : {

	            bdText : _content,

	            bdDesc : _desc,

	            bdUrl : _url,

	            bdPic : _pic,

	            bdMiniList:['mshare','tsina','weixin','qzone','tqq','tieba','people']

	        },

	        share : [{

	            "bdSize" : 24

	        }],

	        image : [{

	            viewType : 'list',

	            viewPos : 'top',

	            viewColor : 'black',

	            viewSize : '16',

	            viewList : ['mshare','tsina','weixin','qzone','tqq','tieba','people']

	        }],

	        selectShare : [{

	            "bdselectMiniList" : ['qzone','tqq','kaixin001','bdxc','tqf']

	        }]

	    };

}













       

        

        

</script>    

                    </div>

                </div>

                <div class="articleContent">

                	<p indenttext="　　" noextractcontent="true" style="text-align: center;">深财法〔2025〕20号<br/></p><p indenttext="　　" noextractcontent="true">各区人民政府、大鹏管委会、深汕管委会，各有关单位、前海管理局：<br/></p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　根据《财政部 税务总局关于延续实施粤港澳大湾区个人所得税优惠政策的通知》（财税〔2023〕34号）、《广东省财政厅 广东省科学技术厅 广东省人力资源和社会保障厅 国家税务总局广东省税务局关于进一步贯彻落实粤港澳大湾区个人所得税优惠政策的通知》（粤财税〔2023〕21号）有关规定，结合实际，我们制定了《深圳市粤港澳大湾区个人所得税优惠政策2024纳税年度财政补贴申报指南》，现予印发，请遵照执行。</p><p indenttext="　　" noextractcontent="true" style="text-align: right;">　　深圳市财政局 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;深圳市人力资源和社会保障局</p><p indenttext="　　" noextractcontent="true" style="text-align: right;">　　深圳市科技创新局 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;国家税务总局深圳市税务局</p><p indenttext="　　" noextractcontent="true" style="text-align: right;">　　2025年5月29日</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　</p><p indenttext="　　" noextractcontent="true" style="text-align: center;"><strong>深圳市粤港澳大湾区个人所得税优惠政策2024纳税年度财政补贴申报指南</strong></p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　一、政策依据</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　（一）《财政部 税务总局关于延续实施粤港澳大湾区个人所得税优惠政策的通知》（财税〔2023〕34号）</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　（二）《广东省财政厅 广东省科学技术厅 广东省人力资源和社会保障厅 国家税务总局广东省税务局关于进一步贯彻落实粤港澳大湾区个人所得税优惠政策的通知》（粤财税〔2023〕21号）</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　二、适用范围</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　在深圳市行政区域范围内工作、符合条件的境外人才（含高端人才、紧缺人才），可对其2024纳税年度（即2024年1月1日至2024年12月31日）在深圳市缴纳的个人所得税已缴税额超过按应纳税所得额15%计算的税额部分申请财政补贴。该补贴免征个人所得税。2024纳税年度每个纳税人的个人所得税财政补贴额最高不超过500万元。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　境外人才2024年领取过深圳市、各区高层次人才奖励补贴，“鹏城孔雀计划”特聘岗位奖励，市级全球人才汇聚、特聘岗位、培养支持项目奖励等人才奖励或补贴，实际补贴额需扣减已发放的人才奖励或补贴金额。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　境外人才同时符合粤港澳大湾区个人所得税优惠政策和前海深港现代服务业合作区个人所得税优惠政策、河套深港科技创新合作区深圳园区个人所得税优惠政策的，可自行选择享受其中一项优惠，不得同时享受多项优惠。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　三、申报条件</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　（一）基本条件</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　<strong>1.身份条件：</strong>申请人属于香港、澳门永久性居民，取得香港入境计划（优才、专业人士及企业家）的香港居民，台湾地区居民，外国国籍人士，或取得国外长期居留权的回国留学人员和海外华侨。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　在纳税年度内，申请人因身份发生变化而符合上述身份条件的，自身份变化次月起，享受财政补贴。在纳税年度内，申请人因身份发生变化不再符合上述身份条件的，自身份变化次月起，不再享受财政补贴。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　<strong>2.工作条件：</strong>申请人纳税年度内在深圳市科技创新、重点发展产业或哲学社会科学领域工作，与在深圳市注册的企业和其他机构签订劳动合同、派遣合同或劳务合同，且纳税年度内在深圳市工作累计满90天以上（不含90天），并在深圳市依法缴纳个人所得税。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　属于高端人才的申请人，申请财政补贴时应仍在深圳市工作；属于紧缺人才的申请人，申请财政补贴时应仍在深圳市相关紧缺岗位任职。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　申请人一个纳税年度在深圳市工作的天数，是指在深圳市工作期间在深圳市实际停留的天数。申请人在深圳市停留的当天不足24小时的，按照半天计算在深圳市的工作天数。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　<strong>3.诚信条件：</strong>申请人应当遵守法律法规、科研伦理和科研诚信，依法纳税，申请时未被列入严重失信主体名单。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　（二）人才条件</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　<strong>1.境外人才粤港澳大湾区个人所得税优惠政策财政补贴（高端人才）。</strong>申请人应符合省落实粤港澳大湾区个人所得税优惠政策规定的人才范围，且符合《深圳市粤港澳大湾区个人所得税优惠政策财政补贴项目高端人才指导目录》标准（附件1）。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　高端人才资格的时点，以国家、广东省、深圳市各类重大人才工程管理机关的人才认定文件（发文名单）、确认函、证书证件的生效或有效时间为准。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　<strong>2.境外人才粤港澳大湾区个人所得税优惠政策财政补贴（紧缺人才）。</strong>申请人应符合省落实粤港澳大湾区个人所得税优惠政策规定的人才范围，且符合《深圳市粤港澳大湾区个人所得税优惠政策财政补贴项目紧缺人才指导目录》标准（附件2）。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　紧缺人才资格的时点，以工作单位说明申请人所从事岗位（工种）的生效或有效时间为准。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　<strong>3.享受补贴的时点规则。</strong>申请人获得高端人才、紧缺人才资格时点处于纳税年度内的，可享受相应纳税年度的财政补贴；高端人才、紧缺人才资格时点在纳税年度结束以后才生效的，不享受相应的纳税年度财政补贴。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　四、申报时间</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　2025年6月1日至2025年7月31日受理2024纳税年度个人所得税优惠政策财政补贴申请。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　五、申报材料</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　（一）申请人有效身份证明证件</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　1.香港、澳门永久性居民提交永久性港澳居民身份证、港澳居民来往内地通行证。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　2.取得香港入境计划（优才、专业人士及企业家）的香港居民提交香港居民身份证、香港入境事务处签发的相关入境证件以及香港税务局开具的香港特别行政区居民身份证明书。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　3.台湾地区居民提交台湾居民来往大陆通行证。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　4.外国国籍人士提交护照、有效签证证件，或外国人永久居留身份证。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　5.取得国外长期居留权的回国留学人员和海外华侨提交中国护照、中国身份证、国外长期（或永久）居留凭证和出入境记录。其中，回国留学人员还应当提交教育部留学服务中心开具的《国外学历学位认证书》。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　（二）申请人人才资质材料</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　1.高端人才：提供获国家、广东省政府、深圳市政府部门认定的高端人才有关证书、聘书、确认函、证明函、认定文件、外国人来华工作许可证（或许可通知、社保卡等已获批外国人来华工作许可的证明）等材料，以及工作单位就单位属性、主营业务、申请人所从事岗位的说明材料。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　2.紧缺人才：提供工作单位就其任职岗位以及该岗位属于科研岗位、教研岗位、技术岗位、技能岗位或管理岗位的说明，工作单位就申请人本人属于本单位科研人员、教研人员、技术骨干、技能骨干或高级管理人员及其从业经验、工作胜任情况的说明，以及工作单位对单位属性、主营业务的说明。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　（三）申请人与单位签订的劳动、派遣、劳务合同</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　1.申请人因工作关系在深圳市注册的企业和其他机构任职、受雇的，提供申请人与单位所签订的劳动合同；申请人属由中国境外雇主派遣的，提供该申请人的中国境外雇主与深圳市接收企业签订的派遣合同。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　2.申请人因工作关系在深圳市提供独立个人劳务的，提供申请人与在深圳市设立的企业、机构所签订的劳务合同。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　（四）申请人银行账户资料</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　申请人本人在中国内地开设和已激活的I类银行结算账户（即全功能账户）资料，包括提供含申请人本人的开户银行、银行账号、开户名的存折或银行卡复印件。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　（五）申请人和申请单位承诺、说明材料</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　申请人提供本人对所提交资料真实性负责、遵守法律法规、纳税年度在深圳市工作满90天以上、申请财政补贴时仍在深圳市工作等情况的承诺书（模板见附件3、4）。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　申请单位提供对单位属性、主营业务以及申请人从事岗位、工作情况、是否仍在职等情况的说明书（模板见附件5）。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　（六）入选人才工程或人才项目获得的补贴性所得相关材料</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　申请人获得深圳市政府或政府工作部门、直属机构人才工程或人才项目奖励、补贴的材料，包括但不限于相关经费下达通知等。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　六、办理程序</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　（一）个人申请</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　申请人、申请单位在广东省统一身份认证平台(地址:<a href="http://tyrz.gd.gov.cn/" target="_self" title="广东省统一身份认证平台">http://tyrz.gd.gov.cn/</a>）注册用户，个人用户需进行实名验证。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　申请人于2025年6月1日至2025年7月31日期间在广东省政务服务网(地址:<a href="https://www.gdzwfw.gov.cn/" target="_self" title="广东省政务服务网">https://www.gdzwfw.gov.cn/</a>）搜索“深圳市境外高端人才和紧缺人才个人所得税财政补贴”，提出补贴申请，提交相关证明材料，作出书面承诺，并提交工作单位审核。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　（二）单位申报</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　申请单位对申请人的申报信息及材料进行审核后，于2025年7月31日前在申报系统提交申请。申请单位需一并提交对单位属性、主营业务及申请人从事岗位等情况的说明。申请单位在首次提交申请时，应一并选择本单位对应的产业类别提交产业领域主管部门审核。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　申请人以个人劳务申报的，无需通过单位申报（已纳税额中包含工资、薪金所得的，须通过单位申报）。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　（三）申报受理</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　受理机关自收到申请之日起5个工作日内作出受理或不予受理的决定，对于申报材料不齐的应当告知申请人或申请单位补正。申请人应当在7个工作日内补正材料，逾期不补正的，受理机关不予受理并告知申请人或申请单位。符合容缺受理条件的，按照容缺受理的相关规定执行。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　（四）初步审核</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　受理机关受理后作出初步审核意见。情况复杂需要延长的，应当告知申请人或申请单位。初步审核未通过的，退回并告知申请单位或申请人。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　（五）集中审核</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　集中审核部门对初审结果开展集中审核，复核申报内容，提出享受优惠政策补贴的高端人才名单和紧缺人才名单，并按程序报批。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　（六）补贴核算</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　集中审核通过后，受理机关应告知申请人5个工作日内登录申报系统获取年度纳税数据予以确认，并提交至受理机关进行补贴核算。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　申请人可预先登录自然人电子税务局WEB端（<a href="https://etax.chinatax.gov.cn/" target="_self" title="自然人电子税务局">https://etax.chinatax.gov.cn/</a>），通过“特色应用”—“人才补贴（奖励）个税数据”查询页面，按年度查询纳税数据并授权发送至受理机关。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　申请人身份信息应与申报缴纳个人所得税时所使用的身份信息保持一致。申请人使用多个不同身份证明登记纳税的，须在税务部门进行税务并档后申报，并提交其他身份证明文件。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　列入拟发放补贴名单，受理机关核算补贴金额，经核算的补贴金额与申请补贴金额不一致的，应当及时告知申请人。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　（七）异议处理</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　申请人对初步审核意见有异议的，应当在被告知之日起7日内提出申诉，逾期不再受理，受理机关应当在15日内复核，并告知申请人复核结果。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　申请人对补贴金额有异议的，应当在被告知之日起15日内，向受理机关提交补贴金额重新核算申请，受理机关应当在60日内完成核算。核算后有差额的，应予以修改后告知申请人。申请人在被告知之日起15日内未对补贴金额再次提出异议的，受理机关按流程发放补贴。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　（八）补贴发放</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　受理机关对照发放名单和核算补贴金额，通过财政国库集中支付系统将资金直接拨付至申请人指定的金融社保卡账户或其他个人银行账户。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　七、办理机关及咨询电话</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　深圳市各区人力资源部门负责办理辖区内境外高端人才和紧缺人才粤港澳大湾区个人所得税优惠政策财政补贴，咨询电话如下。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　福田区人力资源局：0755-82918537</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　罗湖区人力资源局：0755-25434833、25438383</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　盐田区人力资源局：0755-25351655</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　南山区人力资源局：0755-86628407、86199403</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　宝安区人力资源局：0755-27664525</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　龙岗区人力资源局：0755-28933851</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　龙华区人力资源局：0755-23336194</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　坪山区人力资源局：0755-85209437、85209307</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　光明区人力资源局：0755-88211317、88212064</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　大鹏新区组织人事局：0755-88158769</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　深汕特别合作区组织人事局：0755-22100560</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　前海国际人才服务中心：0755-88981800</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　八、其他事项</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　（一）补贴业务实行信用承诺制申报，申请人、申请单位对填报信息的真实性、准确性、完整性负责。对于申请人、申请单位所作出的书面承诺和说明，受理、审核机关有权进行信用监管和事后稽核，申请人、申请单位应配合提供相应证明材料。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　（二）申请人和申请单位应如实提供申请材料，并对申请材料完整性、真实性和准确性负责。对于虚报、冒领、骗取财政补贴资金的行为，一经查实，取消享受优惠政策的资格，追回已安排的财政补贴，并依据《财政违法行为处罚处分条例》（国务院令第427号）等法律法规予以处理，涉嫌犯罪的，移交司法机关依法追究刑事责任。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　（三）本申报指南自2025年6月1日起实施，有效期1年，2024纳税年度境外高端人才和紧缺人才粤港澳大湾区个人所得税优惠政策财政补贴申请、审核、发放，按照本申报指南执行。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　附件：1.深圳市粤港澳大湾区个人所得税优惠政策财政补贴项目高端人才指导目录</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;2.深圳市粤港澳大湾区个人所得税优惠政策财政补贴项目紧缺人才指导目录</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;3.申请个税优惠补贴承诺书（适用高端人才）</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;4.申请个税优惠补贴承诺书（适用紧缺人才）</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5.单位属性、主营业务及申请人从事岗位的说明</p><p><br/></p>

                </div>

                <div class="relatedAccessory">

                    <h4>相关附件</h4>

                    	<p class="accessoryText">附件1：<i><a href="https://szfb.sz.gov.cn/attachment/1/1588/1588964/12203332.doc" target="_blank">1.深圳市粤港澳大湾区个人所得税优惠政策财政补贴项目高端人才指导目录.doc点击下载</a></i></p>

                    	<p class="accessoryText">附件2：<i><a href="https://szfb.sz.gov.cn/attachment/1/1588/1588566/12203332.doc" target="_blank">2.深圳市粤港澳大湾区个人所得税优惠政策财政补贴项目紧缺人才指导目录.doc点击下载</a></i></p>

                    	<p class="accessoryText">附件3：<i><a href="https://szfb.sz.gov.cn/attachment/1/1588/1588565/12203332.doc" target="_blank">3.申请个税优惠补贴承诺书（适用高端人才）.doc点击下载</a></i></p>

                    	<p class="accessoryText">附件4：<i><a href="https://szfb.sz.gov.cn/attachment/1/1588/1588564/12203332.doc" target="_blank">4.申请个税优惠补贴承诺书（适用紧缺人才）.doc点击下载</a></i></p>

                    	<p class="accessoryText">附件5：<i><a href="https://szfb.sz.gov.cn/attachment/1/1588/1588957/12203332.doc" target="_blank">5.单位属性、主营业务及申请人从事岗位的说明.doc点击下载</a></i></p>

                </div>

                <div class="relatedAccessory fileInfos none">

                    <h4>附件</h4>

                </div>

            </div>



        </div>

    </div>

    <!--政策原文-->

</div>

 

<!--底部脚本-->



<!-- <link rel="stylesheet" href="/sqzc/css/gvmFoot.css" media="all"> -->

<link rel="stylesheet" href="/sqzc/css/foot.css" media="all">

<script type="text/javascript" src="/sqzc/js/other/foot.js"> </script>



<!--底部脚本-->

<footer class="gvmFoot gvmFooter">

    <!--底部导航条-->

    <div class="Links">

        <div class="linkCon">

            <div class="button"><a href="http://www.gov.cn/">中国政府网</a></div>

            <span class="shu"></span>



            <div class="cusSelect" id="gjbw">

                <div class="cusTitle">国务院部门网站</div>

                <ul class="cusOpSet" style="bottom: 40px; left: -15px; right: unset; display: none;">

                    <li class="cusOption"><a  target="_blank" href="http://www.fmprc.gov.cn/web/">外交部</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.mod.gov.cn/">国防部</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.ndrc.gov.cn/">国家发展和改革委员会</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.moe.gov.cn/">教育部</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.most.gov.cn/">科学技术部</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.miit.gov.cn/">工业和信息化部</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.seac.gov.cn/">国家民族事务委员会</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.mps.gov.cn/">公安部</a></li>

                    <li class="cusOption"><a  target="_blank" href="javascript:;">国家安全部</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.mca.gov.cn/">民政部</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.moj.gov.cn/">司法部</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.mof.gov.cn/index.htm">财政部</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.mohrss.gov.cn/">人力资源和社会保障部</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.mnr.gov.cn/">自然资源部</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.mee.gov.cn/">生态环境部</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.mohurd.gov.cn/">住房和城乡建设部</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.mot.gov.cn/">交通运输部</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.mwr.gov.cn/">水利部</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.moa.gov.cn/">农业农村部</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.mofcom.gov.cn/">商务部</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.mct.gov.cn/">文化和旅游部</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.nhfpc.gov.cn/">国家卫生健康委员会</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.mva.gov.cn/" target="_blank">退役军人事务部</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.chinasafety.gov.cn/index.shtml">应急管理部</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.pbc.gov.cn/">人民银行</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.audit.gov.cn/">审计署</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.china-language.gov.cn/">国家语言文字工作委员会</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.safea.gov.cn/">国家外国专家局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.cnsa.gov.cn/">国家航天局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.caea.gov.cn/">国家原子能机构</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.soa.gov.cn/">国家海洋局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://nnsa.mep.gov.cn/">国家核安全局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.sasac.gov.cn/">国务院国有资产监督管理委员会</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.customs.gov.cn/">海关总署</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.chinatax.gov.cn/">国家税务总局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://samr.saic.gov.cn/">国家市场监督管理总局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.sapprft.gov.cn/">国家广播电视总局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.sport.gov.cn/">国家体育总局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.stats.gov.cn/">国家统计局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.cidca.gov.cn/">国家国际发展合作署</a></li>

                    <li class="cusOption"><a  target="_blank" href="javascript:;">国家医疗保障局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.counsellor.gov.cn/">国务院参事室</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.ggj.gov.cn/">国家机关事务管理局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.cnca.gov.cn/">国家认证认可监督管理委员会</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.sac.gov.cn/">国家标准化管理委员会</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.ncac.gov.cn/">国家新闻出版署（国家版权局）</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.sara.gov.cn/">国家宗教事务局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.hmo.gov.cn/">国务院港澳事务办公室</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.gov.cn/guoqing/2018-06/22/content_5300522.htm">国务院研究室</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.gqb.gov.cn/">国务院侨务办公室</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.gwytb.gov.cn/">国务院台湾事务办公室</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.cac.gov.cn/">国家互联网信息办公室</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.scio.gov.cn/index.htm">国务院新闻办公室</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://203.192.6.89/xhs/">新华通讯社</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.cas.ac.cn/">中国科学院</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://cass.cssn.cn/">中国社会科学院</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.cae.cn/">中国工程院</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.drc.gov.cn/">国务院发展研究中心</a></li>

                    <li class="cusOption"><a  target="_blank" href="javascript:;">中央广播电视总台</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.cma.gov.cn/">中国气象局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.cbrc.gov.cn/">中国银行保险监督管理委员会</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.csrc.gov.cn/pub/newsite/">中国证券监督管理委员会</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.nsa.gov.cn/">国家行政学院</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.gjxfj.gov.cn/">国家信访局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.chinagrain.gov.cn/index.html">国家粮食和物资储备局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.nea.gov.cn/">国家能源局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.sastind.gov.cn/">国家国防科技工业局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.tobacco.gov.cn/html/">国家烟草专卖局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.mps.gov.cn/n2254996/">国家移民管理局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.forestry.gov.cn/">国家林业和草原局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.nra.gov.cn/">国家铁路局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.caac.gov.cn/index.html">中国民用航空局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.spb.gov.cn/">国家邮政局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.sach.gov.cn/">国家文物局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.satcm.gov.cn/">国家中医药管理局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.chinacoal-safety.gov.cn/">国家煤矿安全监察局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.safe.gov.cn/">国家外汇管理局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.nmpa.gov.cn/WS04/CL2042/">国家药品监督管理局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.cnipa.gov.cn">国家知识产权局</a></li>

                    <li class="cusOption"><a  target="_blank" href="javascript:;">出入境管理局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.forestry.gov.cn/">国家公园管理局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.scs.gov.cn/">国家公务员局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.saac.gov.cn/">国家档案局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.gjbmj.gov.cn">国家保密局</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.oscca.gov.cn/">国家密码管理局</a></li>

                </ul>

            </div>

            <span class="shu"></span>



            <div class="cusSelect" id="sqszf">

                <div class="cusTitle">各省区市网站</div>

                <ul class="cusOpSet" style="bottom: 40px; left: -15px; right: unset; display: none;">

                    <li class="cusOption"><a  target="_blank" href="http://www.beijing.gov.cn/">北京</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.tj.gov.cn/">天津</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.hebei.gov.cn/">河北</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.shanxi.gov.cn">山西</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.nmg.gov.cn/">内蒙古</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.ln.gov.cn">辽宁</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.jl.gov.cn">吉林</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.hlj.gov.cn">黑龙江</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.shanghai.gov.cn/">上海</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.jiangsu.gov.cn/">江苏</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.zhejiang.gov.cn">浙江</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.ah.gov.cn/">安徽</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.fujian.gov.cn">福建</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.jiangxi.gov.cn">江西</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.shandong.gov.cn/">山东</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.henan.gov.cn/">河南</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.hubei.gov.cn/">湖北</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.hunan.gov.cn/">湖南</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.gd.gov.cn/">广东</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.gxzf.gov.cn/">广西</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.hainan.gov.cn/">海南</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.cq.gov.cn/">重庆</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.sc.gov.cn">四川</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.guizhou.gov.cn">贵州</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.yn.gov.cn/">云南</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.xizang.gov.cn/">西藏</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.shaanxi.gov.cn/">陕西</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.gansu.gov.cn/">甘肃</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.nx.gov.cn/">宁夏</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.qh.gov.cn/">青海</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.xinjiang.gov.cn/">新疆</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.gov.hk/">香港</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://portal.gov.mo/web/guest/welcomepage">澳门</a></li>

                    <!-- <option disabled="">台湾</option> -->

                    <li class="cusOption"><a>台湾</a></li>

                </ul>

            </div>

            <span class="shu"></span>



            <div class="cusSelect" id="zgajg">

                <div class="cusTitle">驻港澳机构网站</div>

                <ul class="cusOpSet" style="bottom: 40px; left: -15px; right: unset; display: none;">

                    <li class="cusOption"><a  target="_blank" href="http://www.locpg.gov.cn/">中央政府驻港联络办</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.zlb.gov.cn/">中央政府驻澳门联络办</a></li>

                </ul>

            </div>



            <span class="shu"></span>

            <div class="cusSelect" id="szf">

                <div class="cusTitle">省政府机构网站</div>

                <ul class="cusOpSet">

                    <li class="cusOption"><a href="http://www.gd.gov.cn/">省政府办公厅</a></li>

                    <li class="cusOption"><a href="http://drc.gd.gov.cn">省发展改革委</a></li>

                    <li class="cusOption"><a href="http://edu.gd.gov.cn">省教育厅</a></li>

                    <li class="cusOption"><a href="http://gdstc.gd.gov.cn">省科技厅</a></li>

                    <li class="cusOption"><a href="http://gdii.gd.gov.cn">省工业和信息化厅</a></li>

                    <li class="cusOption"><a href="http://mzzjw.gd.gov.cn">省民族宗教委</a></li>

                    <li class="cusOption"><a href="http://gdga.gd.gov.cn/">省公安厅</a></li>

                    <li class="cusOption"><a href="http://smzt.gd.gov.cn">省民政厅</a></li>

                    <li class="cusOption"><a href="http://sft.gd.gov.cn">省司法厅</a></li>

                    <li class="cusOption"><a href="http://czt.gd.gov.cn/">省财政厅</a></li>

                    <li class="cusOption"><a href="http://www.gdhrss.gov.cn/">省人力资源社会保障厅</a></li>

                    <li class="cusOption"><a href="http://nr.gd.gov.cn">省自然资源厅</a></li>

                    <li class="cusOption"><a href="http://gdee.gd.gov.cn/">省生态环境厅</a></li>

                    <li class="cusOption"><a href="http://zfcxjst.gd.gov.cn/">省住房城乡建设厅</a></li>

                    <li class="cusOption"><a href="http://td.gd.gov.cn">省交通运输厅</a></li>

                    <li class="cusOption"><a href="http://slt.gd.gov.cn">省水利厅</a></li>

                    <li class="cusOption"><a href="http://dara.gd.gov.cn">省农业农村厅</a></li>

                    <li class="cusOption"><a href="http://com.gd.gov.cn">省商务厅</a></li>

                    <li class="cusOption"><a href="http://www.gdwht.gov.cn/">省文化和旅游厅</a></li>

                    <li class="cusOption"><a href="http://wsjkw.gd.gov.cn">省卫生健康委</a></li>

                    <li class="cusOption"><a href="http://dva.gd.gov.cn/">省退役军人事务厅</a></li>

                    <li class="cusOption"><a href="http://yjgl.gd.gov.cn">省应急管理厅</a></li>

                    <li class="cusOption"><a href="http://gdaudit.gd.gov.cn/">省审计厅</a></li>

                    <li class="cusOption"><a href="http://gzw.gd.gov.cn">省国资委</a></li>

                    <li class="cusOption"><a href="http://amr.gd.gov.cn">省市场监管局</a></li>

                    <li class="cusOption"><a href="http://gbdsj.gd.gov.cn">省广电局</a></li>

                    <li class="cusOption"><a href="http://www.tyj.gd.gov.cn/">省体育局</a></li>

                    <li class="cusOption"><a href="http://stats.gd.gov.cn">省统计局</a></li>

                    <li class="cusOption"><a href="http://hsa.gd.gov.cn">省医保局</a></li>

                    <li class="cusOption"><a href="http://hmo.gd.gov.cn/">省港澳办</a></li>

                    <li class="cusOption"><a href="http://gdjr.gd.gov.cn">省地方金融监管局</a></li>

                    <li class="cusOption"><a href="http://www.gdcss.gd.gov.cn/">省参事室 </a></li>

                    <li class="cusOption"><a href="http://gdwsxf.gd.gov.cn/">省信访局</a></li>

                    <li class="cusOption"><a href="http://zfsg.gd.gov.cn/">省政务服务数据管理局</a></li>

                    <li class="cusOption"><a href="http://gdgrain.gd.gov.cn">省粮食和储备局</a></li>

                    <li class="cusOption"><a href="http://gdnpo.gd.gov.cn">省社会组织管理局</a></li>

                    <li class="cusOption"><a href="http://gdjyj.gd.gov.cn">省监狱局</a></li>

                    <li class="cusOption"><a href="http://gdjdj.gd.gov.cn">省戒毒局</a></li>

                    <li class="cusOption"><a href="http://lyj.gd.gov.cn">省林业局</a></li>

                    <li class="cusOption"><a href="http://szyyj.gd.gov.cn">省中医药局</a></li>

                    <li class="cusOption"><a href="http://mpa.gd.gov.cn">省药监局</a></li>

                    <li class="cusOption"><a href="http://gdio.southcn.com/">省新闻办</a></li>

                    <li class="cusOption"><a href="http://www.bmj.gd.gov.cn/">省保密局</a></li>

                    <li class="cusOption"><a href="http://www.gdas.gd.cn/">省科学院</a></li>

                    <li class="cusOption"><a href="http://www.gdass.gov.cn/">省社科院</a></li>

                    <li class="cusOption"><a href="http://www.gdaas.cn/">省农科院</a></li>

                    <li class="cusOption"><a href="http://gdyjzx.gd.gov.cn">省发展研究中心</a></li>        

                    <li class="cusOption"><a href="http://dfz.gd.gov.cn/">省地方志办</a></li>

                     <li class="cusOption"><a href="http://djj.gd.gov.cn">省代建局</a></li>                       

                    <li class="cusOption"><a href="http://dzj.gd.gov.cn">省地质局</a></li>

                    <li class="cusOption"><a href="http://gdhgy.gd.gov.cn">省核工业地质局</a></li>

                    <li class="cusOption"><a href="http://coop.gd.gov.cn">省供销社</a></li>

                    <li class="cusOption"><a href="http://eea.gd.gov.cn">省考试院</a></li>

                    <li class="cusOption"><a href="http://gdae.gdedu.gov.cn/">省教育研究院</a></li>

                    <li class="cusOption"><a href="http://www.fenxi.com.cn/client/home/<USER>">省测试分析研究所</a></li>

                    <li class="cusOption"><a href="http://www.gdppnet.com//">省生产力促进中心</a></li>

                    <li class="cusOption"><a href="http://www.gdsc.cn/">省科学中心</a></li>

                    <li class="cusOption"><a href="http://www.gdsi.gov.cn/">省社保基金管理局</a></li>

                    <li class="cusOption"><a href="http://www.gdadri.com/">省建筑设计院</a></li>

                    <li class="cusOption"><a href="http://swj.gd.gov.cn">省水文局</a></li>

                    <li class="cusOption"><a href="http://bjly.gd.gov.cn">省北江流域管理局</a></li>

                    <li class="cusOption"><a href="http://www.gdghospital.org.cn/">省人民医院</a></li>

                    <li class="cusOption"><a href="http://cdcp.gd.gov.cn">省疾控中心</a></li>

                    <li class="cusOption"><a href="http://www.tyj.gd.gov.cn/4294295/4308053.html">省体育二沙运动训练中心</a></li>

                    <li class="cusOption"><a href="http://www.gqi.org.cn/">省质检院</a></li>

                    <li class="cusOption"><a href="http://www.gdysdz.com/">省有色金属地质局</a></li>

                    <li class="cusOption"><a href="http://www.szdzj.cn/">深圳市地质局</a></li>

                </ul>

            </div>



            <span class="shu"></span>

            <div class="cusSelect" id="sxzf">

                <div class="cusTitle">地级以上市网站</div>

                <ul class="cusOpSet" style="bottom: 40px; left: -15px; right: unset; display: none;">

                    <li class="cusOption"><a  target="_blank" href="http://www.gz.gov.cn/">广州</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.sz.gov.cn/">深圳</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.zhuhai.gov.cn/">珠海</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.shantou.gov.cn/">汕头</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.foshan.gov.cn/">佛山</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.sg.gov.cn/ ">韶关</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.heyuan.gov.cn/">河源</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.meizhou.gov.cn/">梅州</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.huizhou.gov.cn/">惠州</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.shanwei.gov.cn/">汕尾</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.dongguan.gov.cn/">东莞</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.zs.gov.cn/">中山</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.jiangmen.gov.cn/">江门</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.yangjiang.gov.cn/">阳江</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.zhanjiang.gov.cn/">湛江</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.maoming.gov.cn/">茂名</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.zhaoqing.gov.cn/">肇庆</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.gdqy.gov.cn/">清远</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.chaozhou.gov.cn/">潮州</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.jieyang.gov.cn/">揭阳</a></li>

                    <li class="cusOption"><a  target="_blank" href="http://www.yunfu.gov.cn/">云浮</a></li>

                </ul>

            </div>

        </div>

    </div>

    <!--底部导航条-->

    

    <!--底部主体-->

    <div class="info">

        <div class="info-itm zficon">

            <div class="jiucuo">

                <script id="_jiucuo_" sitecode="4400000131" src="/sqzc/js/other/jiucuo.js"></script>

                <span id="_span_jiucuo">

                    <img onclick="Link('4400000131')" style="margin:0;border:0;cursor: pointer;" src="/sqzc/images/jiucuo.png?v=4400000131">

                </span>

            </div>

            <div class="ideConac">

                <span id="_ideConac">

                	<a href="http://bszs.conac.cn/sitename?method=show&amp;id=058751C9AA454F26E053012819ACD379"><img src="/sqzc/images/red2.png"></a>

                </span>

            </div>

        </div>

        <div class="info-itm  wzinfo">

            <div class="wzinfo-itm active">网站信息</div>

            <div class="wzinfo-itm">

                <a href="http://www.gd.gov.cn/yw/content/post_100915.html" target="_blank">关于本网</a>

                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;

                <a href="http://www.gd.gov.cn/yw/content/post_2169593.html" target="_blank">网站声明</a>

            </div>

            <div class="wzinfo-itm">

                <a href="http://www.gd.gov.cn/yw/content/post_104984.html" target="_blank">网站地图</a>

            </div>

        </div>

        <div class="info-itm  wzinfo">

            <div class="wzinfo-itm active">联系我们</div>

            <div class="wzinfo-itm">

                <p>020-83135078</p>

                <p style="font-size: 12px; color: #777;">仅受理网站建设维护相关事宜</p>

            </div>

            <div class="wzinfo-itm"><EMAIL></div>

        </div>

		<div class="info-itm  wzinfo xmtjz">

            <div class="wzinfo-itm active" style="cursor:auto;">新媒体矩阵</div>

            <div class="wzinfo-itm code1" >

                <img src="/sqzc/images/qr.png">网站官方微信公众号

            </div>

            <div class="wzinfo-itm">

                <div class="code2" style="display:inline-block;width: 47%;"> 

                    <img src="/sqzc/images/qr.png">粤省事小程序

                 </div>

                <div class="code3" style="display:inline-block;width: 45%; float:right;">

                    <img src="/sqzc/images/qr.png">粤商通APP

                </div>

            </div>

        </div>

        <div class="QrCode qr_1 hide">

            <img src="/sqzc/images/qrCode_3.png">

        </div>

        <div class="QrCode qr_2 hide">

            <img src="/sqzc/images/qrCode_2.png">

        </div>

        <div class="QrCode qr_3 hide">

            <img src="/sqzc/images/qrCode_4.jpg">

        </div>



    </div>

    <!--底部主体-->



    <!--页尾-->

    <div class="copyright">

        <div class="footCon">

            <span>主办：&nbsp;&nbsp;&nbsp;广东省人民政府办公厅&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>

            <span>承办：&nbsp;&nbsp;&nbsp;南方新闻网&nbsp;&nbsp;&nbsp;数字广东网络建设有限公司</span>

            <span class="right"><p>版权所有：广东省人民政府门户网站</p><a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=44010402001160"><p class="gab"><img src="/sqzc/images/icon_ga.png" style="float:left; width:20px; height:20px; margin-top: 14px;">粤公网安备 44010402001160号</p></a><p>粤ICP备05070829号-1&nbsp;&nbsp;&nbsp;网站标识码4400000131</p></span>

        </div>

    </div>

    <!--页尾-->

</footer>

<div class="footer_app">

	<div class="layui-container" style="padding: 0;">  

	  <div class="layui-row layui-col-space10 fujian">

		<div class="layui-col-sm3 layui-col-xs3">	    	

			<div class="fujianList" onclick="goConsult();">

		    	<i class="iconfont icon-file-text-o"></i><p>企业诉求</p>

	    	</div>

	    </div>

	    <div class="layui-col-sm3 layui-col-xs3">

		    <div class="fujianList" onclick="service();">

		    	<i class="iconfont icon-zixun1"></i><p>客 服</p>

	    	</div>

	    </div>

	    <div class="layui-col-sm3 layui-col-xs3">

		    <div class="fujianList" onclick="help();">

		    	<i class="iconfont icon-bangzhu5" ></i><p>帮 助</p>

	    	</div>

	    </div>

	    <div class="layui-col-sm3 layui-col-xs3">

		    <div class="fujianList" onclick="suggest();">

		    	<i class="iconfont icon-tousujianyi" ></i><p>网站纠错</p>

	    	</div>

	    </div>

	    

	  </div>

    </div>

	<div class="banquan">版权：广东省人民政府门户网站</div>

</div>





<!--底部脚本-->



<script>

$(function () {

//  底部最新脚本-----------------新增

  $(".code1").on("click", function () {

      $(".qr_2").removeClass("hide").addClass("hide");

      $(".qr_3").removeClass("hide").addClass("hide");

      $(".qr_1").toggleClass("hide");



  });

  $(".code2").on("click", function () {

      $(".qr_1").removeClass("hide").addClass("hide");

      $(".qr_3").removeClass("hide").addClass("hide");

      $(".qr_2").toggleClass("hide");

  });

  

  $(".code3").on("click", function () {

      $(".qr_1").removeClass("hide").addClass("hide");

      $(".qr_2").removeClass("hide").addClass("hide");

      $(".qr_3").toggleClass("hide");

  });

  //            底部最新脚本-----------------新增



})

//企业诉求

function goConsult() {

	  window.open('https://ysx.gdzwfw.gov.cn/dashboard');

}

//智能客服

function service() {

	  //window.open('http://znkf.digitalgd.com.cn/gdbs/customer.html?CId=01');

	  window.open('https://rec.southcn.com/');

}

//交流互动

function communicate(){

	  window.open('/sqzc/m/cms/consult/toLeaveMessage?title=');

}

//帮助

function help(){

	if(browserRedirect()){

		window.location.href = '/sqzc/m/cms/help';

	}else{

		window.open('/sqzc/m/cms/help');

	}

	

}

//投诉建议

function suggest(){

	  /* if(browserRedirect()){

	  		window.location.href = '/sqzc/m/cms/suggest';

		}else{

			window.open('/sqzc/m/cms/suggest');;

		} */

	  if(browserRedirect()){

	  		window.location.href = '/sqzc/m/dept/questionnaire/toQuestionnaireInfo?id=238c5300f7884710a7615e5296ed2e16';

		}else{

			window.open('/sqzc/m/dept/questionnaire/toQuestionnaireInfo?id=238c5300f7884710a7615e5296ed2e16');;

		}

}  

</script><!-- 腳本國際化 -->

<script type="text/javascript">

	var _i18n_validate_required = "<span class='hidden'></span>";//"请填写此信息！";

	var _i18n_validate_remote = "请修正该字段！";

	var _i18n_validate_email = "请输入正确格式的电子邮件！";

	var _i18n_validate_url = "请输入合法的网址！";

	var _i18n_validate_date = "请输入合法的日期！";

	var _i18n_validate_dateISO = "请输入正确的日期 (ISO)！";

	var _i18n_validate_number = "请输入正确的数字！";

	var _i18n_validate_digits = "只能输入整数！";

	var _i18n_validate_creditcard = "请输入合法的信用卡号！";

	var _i18n_validate_equalTo = "请再次输入相同的值！";

	var _i18n_validate_accept = "请输入合法后缀名的信息！";

	var _i18n_validate_maxlength = "请输入最多 {0}位的信息！";

	var _i18n_validate_minlength = "请输入不少于 {0}位的信息！";

	var _i18n_validate_rangelength = "请输入 {0} 至{1}位的信息！";

	var _i18n_validate_range = "请输入介于 {0}和 {1}之间的值！";

	var _i18n_validate_max = "请输入最大为 {0}的值！";

	var _i18n_validate_min = "请输入最小为 {0}的值！	";

	var _i18n_validate_telephone = "请输入正确的手机号码！	";

	var _i18n_validate_idCard = "请输入正确的身份证号码！	";

	var _i18n_bootbox_ok = "确定";

	var _i18n_bootbox_cancel = "取消";

	var _i18n_bootbox_confirm = "确认";

	

	var _i18n_iscroll_release = "释放立刻刷新...";

	var _i18n_iscroll_pulldown = "下拉刷新...";

	var _i18n_iscroll_pullup = "上拉加载更多...";

	var _i18n_iscroll_loading = "加载中...";

	var _i18n_iscroll_nomore = "沒有更多！";

	var _i18n_iscroll_loaderror = "加载出错！";

	

	var _i18n_app_set_collect_name="";

	var _i18n_app_common_collect_cancel="取消收藏";

	var _i18n_app_home_menu_collect="收藏 ";

	var admin_common_success="成功";

	var admin_common_fail="失败";

	var admin_common_cancel="取消";

	

	var _i18n_dialog_type_default = "消息";

	var _i18n_dialog_type_info = "消息";

	var _i18n_dialog_type_primary = "消息";

	var _i18n_dialog_type_success = "成功";

	var _i18n_dialog_type_warning = "警告";

	var _i18n_dialog_type_danger = "危险";

	var _i18n_dialog_button_ok = "确定";

	var _i18n_dialog_button_cancel = "取消	";

	var _i18n_dialog_button_confirmation = "确定";

</script>

<script src="/sqzc/js/main.js?v=20211025&t=1.152" type="text/javascript" charset="utf-8"></script><script type="text/javascript" src="/sqzc/js/layui/layui.js"></script>

<script type="text/javascript" src="/sqzc/js/other/html5shiv.js"> </script>

<script type="text/javascript" src="/sqzc/js/other/respond.min.js"> </script>

<script type="text/javascript" src="/sqzc/js/common_layui.js"> </script>





<script type="text/javascript" src="/sqzc/js/common.js"> </script>

<script type="text/javascript">





var cxt = '/sqzc';

var _apiKey = '';

var _owm = 'false';

if(browserRedirect()){

	var popup = new auiPopup();

}

function hidePopup(){

    popup.hide(document.getElementById("top-left"))

}

function surePopup(){

    popup.hide(document.getElementById("top-left"))

}





//定義js

var app_js = {

	//"bootstrap":"/sqzc/jsFile/jquery/bootstrap/bootstrap.js?v1",

	"footer":"/sqzc/js/footer.js",

	"common":"/sqzc/js/common.js?v=1.16",

	"jquery_form":"/sqzc/js/jquery/form/jquery.form.js",

	"jquery_validate":"/sqzc/js/jquery/validate/jquery.validate.min.js",

	

	"infinitescroll":"/sqzc/jsFile/jquery/infinite-scroll/jquery.infinitescroll.min.js",

	"toastr":"/sqzc/jsFile/jquery/toastr/toastr.min.js",

	"iscroll":"/sqzc/ui/exappui/plugins/iScroll/iscroll.js",

	"jquery_bootbox":"/sqzc/jsFile/jquery/bootbox/bootbox.min.js",

	"jquery_mobiscroll":"/sqzc/jsFile/jquery/mobiscroll/scripts/mobiscroll-2.13.2.full.min.js",

	"jquery_clipboard":"/sqzc/jsFile/jquery/clipboard/clipboard.min.js"

};

	

//定義css

var app_css = {

	"font-awesome":"/sqzc/css/bootstrap/font-awesome.min.css",

	"glyphicons-halflings-regular":"/sqzc/css/bootstrap/glyphicons-halflings-regular.css",

	"toastr":"/sqzc/js/jquery/toastr/toastr.css",

	"jquery_mobiscroll":"/sqzc/jsFile/jquery/mobiscroll/content/mobiscroll-2.13.2.full.min.css"

};

</script>

<script type="text/javascript">

/* head.js(app_js.common);

head.js(app_css["font-awesome"],app_css["glyphicons-halflings-regular"]); */

//加載完成事件

head.ready(function(){

});





function saveLog(msg){

	var a = new Error(msg);

	a.type = 'info';

	var data = {};

	data.msg= a.message;

	data.log_type = a.type;

	data.error =  a.stack;

	common.ajaxFunc("/sqzc/app/common/jslog/savelog", data, "json", function(a) {

	        });

}



/**

     * @param {String}  errorMessage   错误信息

     * @param {String}  scriptURI      出错的文件

     * @param {Long}    lineNumber     出错代码的行号

     * @param {Long}    columnNumber   出错代码的列号

     * @param {Object}  errorObj       错误的详细信息，Anything

     */

window.onerror = function(msg,url,line,col,error){

    //没有URL不上报！上报也不知道错误

    try{

    	try{console.dir(arguments);}catch(e){}

	    if (msg != "Script error." && !url){

	        return true;

	    }

	    //采用异步的方式

	    //我遇到过在window.onunload进行ajax的堵塞上报

	    //由于客户端强制关闭webview导致这次堵塞上报有Network Error

	    //我猜测这里window.onerror的执行流在关闭前是必然执行的

	    //而离开文章之后的上报对于业务来说是可丢失的

	    //所以我把这里的执行流放到异步事件去执行

	    //脚本的异常数降低了10倍

	    setTimeout(function(){

	        var data = {};

	        //不一定所有浏览器都支持col参数

	        col = col || (window.event && window.event.errorCharacter) || 0;

	        data.msg = msg;

	        data.url = location.href;

	        data.line = line;

	        data.col = col;

	       	data.log_type = 'error';

	        if(error&&error.type=="info")

	        data.log_type = 'info';

	        if (!!error && !!error.stack){

	            //如果浏览器有堆栈信息

	            //直接使用

	            data.error = error.stack.toString();

	        }else if (!!arguments.callee){

	            //尝试通过callee拿堆栈信息

	            var ext = [];

	            var f = arguments.callee.caller, c = 3;

	            //这里只拿三层堆栈信息

	            while (f && (--c>0)) {

	               ext.push(f.toString());

	               if (f  === f.caller) {

	                    break;//如果有环

	               }

	               f = f.caller;

	            }

	            ext = ext.join(",");

	            data.error = ext;

	        }

	        //把data上报到后台！

	        /*暂时不上传：common.ajaxFunc("/sqzc/app/common/jslog/savelog", data, "json", function(a) {

	        });*/

	    },0);

    }catch(err){

    	

    }

    return false;

};



//退出（有在main.js里引用，下面也引用）

function __logOut()

{

	//window.location.href="/sqzc/app/platform/login/logout";

	//关闭所有层，并跳转

	common.closeAllModule(__logOut_Callback);

	//common.win_app_open("http://www.baidu.com",abc);

	__logOut_Callback();

}

function __logOut_Callback()  //退出回调函数

{

	common.win_open("/sqzc/app/platform/login/logout");

}





$(function(){

	

	window.app_init_headmore && window.app_init_headmore();

	

	var isCookieNull = getCookie("_view_time");

	if(isCookieNull == null ||isCookieNull.trim() == ""){

		//cookie为空后台访问量加一

		addReadCount();

		setCookie("_view_time","_view_time");	//设置cookie

	}else{  //cookie不为空，不作处理

	

	}

	

})

 

function getCookie(c_name) {  //读取cookies 

    var arr, reg = new RegExp("(^| )" + c_name + "=([^;]*)(;|$)");



    if (arr = document.cookie.match(reg))



        return unescape(arr[2]);

    else

        return ""; 



}



//设置cookie

function setCookie(c_name,c_value) {   

    document.cookie = c_name + "=" + escape(c_value) + ";path=/";  //让 cookie 在根目录下,这样不管是哪个子页面创建的 cookie，所有的页面都可以访问到了，这样才能获取最新的cookie



}



function addReadCount(){ //后台访问量加一

	var params ={};

	var url = "/sqzc/m/pf/count/addCount";

	common.ajaxFunc(url,params,"json",function(result){

		if (result.success) {

			//alert(result.msg);

		}

	});

}



</script>



<script>

	//兼容手机端

	if(browserRedirect()){

		//$('.footer_app,.newPhoneHead').remove();

  		$('.header-title-cont').addClass('showTab');

  		//将表格宽度调整为auto，否则表格内容超出屏幕的情况

  		$(".phoneContTab .articleContent table").css("width","auto");

  		//将p标签调整为自动换行，否则p内容为英文时会有超出屏幕的情况

  		$(".phoneContTab .articleContent p").css("word-break","break-all");

	}else{

		$('#biaoqianTab').remove();

	}

    layui.use(['element','layer'], function(){

        var element = layui.element;

        var layer = layui.layer;

		

        $(function () {

        	//getPolicyRelated();

        	//getRelatedPolicyAnalysis();

        	//getRelatedProject();

    		//getRelatedBidNotice();

    		//getRelatedProjectPublicity();

    		setFileInfos();

    		loadRelatedRes();//获得所有相关资源

    		getRelatedConsult();

    		getCollectStatus();

            var urlText = '';

            console.log(urlText);

            if (urlText == 'project'){

                $('.xxgsType').text('用政策');

                if(browserRedirect()){

                	$('.xxgsContNav').text('公示-原文')

                }else{

                	$('.xxgsContNav').text('公示正文')

                }

            }else {

                $('.xxgsType').text('用政策');

                if(browserRedirect()){

                	$('.xxgsContNav').text('申报通知-原文')

                }else{

                	$('.xxgsContNav').text('申报通知')

                }

            }

            if(!browserRedirect()){

            	shareInit('深圳市财政局 深圳市人力资源和社会保障局 深圳市科技创新局 国家税务总局深圳市税务局关于印发《深圳市粤港澳大湾区个人所得税优惠政策2024纳税年度财政补贴申报指南》的通知', null, null, null); //加载分享js

    		}

            

        });

        $('img').on('error',function(){

        	$(this).hide();

        })

    });

</script>

<script>

function setFileInfos(){

	var fileInfos = JSON.parse('[]');

	if(fileInfos && fileInfos.length >0){

		console.log(fileInfos);

		var fileInfosHtml = "";

		for(var i=0;i < fileInfos.length ; i++){

			var fileInfo = fileInfos[i];

			var fileId = fileInfo.id.replace(" ","");

			var fileTitle = fileInfo.title;

			var fileExt = fileInfo.fileExt;

			var playButton = "";

			if(fileExt == "mp4"){

				playButton = ' <button> 播放 </button> ';

			}

			fileInfosHtml += '<p class="fileInfo accessoryText" fileId="'+fileId+'" fileTitle="'+fileTitle+'" fileExt="'+fileExt+'">附件：<i><a href="javascript:void(0);">'+fileTitle+'.'+fileExt+'</a></i>'+ playButton +'</p>';

		}

		$(".fileInfos").append(fileInfosHtml);

		$(".fileInfos").removeClass('none');

	}

}

$(".fileUrlList,.fileInfos").on("click",".fileInfo i",function(){

	var id = $(this).parent('.fileInfo').attr("fileId");

	var resId = "nfsheqigsxx12203332";

	var libNum = "informationdisclosure";

	params = "id="+id+"&resId="+resId+"&libNum="+libNum;

	var url = "/sqzc/m/cms/common/fileDownload?"+params;

	if(browserRedirect()){

		window.location.href = encodeURI(url.replace(/%/g,'%25'));

	}else{

		window.open(encodeURI(url.replace(/%/g,'%25')));

	}

})



$(".fileUrlList,.fileInfos").on("click",".fileInfo button",function(){

	var that = $(this).parent('.fileInfo');

	var id = that.attr("fileId");

	var fileTitle = that.attr("fileTitle");

	var resId = "nfsheqigsxx12203332";

	var libNum = "informationdisclosure";

	var url =  "/sqzc/m/cms/common/video/"+libNum+"-"+resId+"-attach-"+id+"?title="+fileTitle;

	if(browserRedirect()){

		window.location.href = encodeURI(url.replace(/%/g,'%25'));

	}else{

		window.open(encodeURI(url.replace(/%/g,'%25')));

	}

})

//info接口获取所有相关资源

function loadRelatedRes() {

	var timestamp = Date.parse(new Date());

	var url = '/sqzc/m/cms/common/getRelatedRes?';

	var data = {'id': "nfsheqigsxx12203332","libNum":"informationdisclosure"};//catalogyName:1-部门解读，2-媒体解读

	console.log(url);

	common.ajaxFunc(url,data,'json',function(result){

		console.log(result.data);

		if (result.success) {

			//加载相关解读

			var relatedPolicyAnalysis = result.data[1].relatedPolicyAnalysis;

			var li = '';

			if (relatedPolicyAnalysis == null || relatedPolicyAnalysis.length == 0) {

				li += '<li class="no-data">';

				li += '暂无';

				li += '</li>';

				$('#relatedPolicyAnalysis').append(li);

			}else{

				$.each(relatedPolicyAnalysis, function (i, item){

					li += '<li class="pointer">';

					li += '<p onclick="toPolicyAnalysisInfo(\''+item.id+'\');">' + item.title + '</p>';

					li += '<div class="diagramListBtn">';

					li += '<span class="RlistFrom nowrap">' + item.publisher + '</span>';

					li += '<span class="RlistTime">' + item.publishDate + '</span>';

					li += '</div>';

					li += '</li>';

				});

				$('#relatedPolicyAnalysis').append(li);

			}

			//加载相关政策依据

			var relatedPolicy = result.data[0].relatedPolicy;

			li = '';

			if (relatedPolicy == null || relatedPolicy.length == 0) {

				li += '<li class="no-data">';

				li += '暂无';

				li += '</li>';

				$('#relatedPolicy').append(li);

			}else{

				$.each(relatedPolicy, function (i, item){

					li += '<a href="javascipt:void(0)" onclick="toPolicy(\'' + item.id + '\');">';

					li += '<li>';

					li += '<p>' + item.title + '</p>';

					li += '<div class="diagramListBtn">';

					li += '<span class="RlistFrom nowrap">' + item.publisher + '</span>';

					li += '<span class="RlistTime">' + item.publishDate + '</span>';

					li += '</div>';

					li += '</li>';

					li += '</a>';

				});

				$('#relatedPolicy').append(li);

			}

			

			//加载相关通知

			var relatedBidNotice = result.data[4].relatedBidNotice;

			li = '';

			if (relatedBidNotice == null || relatedBidNotice.length == 0) {

				li += '<li class="no-data">';

				li += '暂无';

				li += '</li>';

				$('#relatedBidNotice').append(li);

			}else{

				$.each(relatedBidNotice, function (i, item){

					li += '<a href="javascipt:void(0)" onclick="toPublicity(\'' + item.id + '\');">';

					li += '<li>';

					li += '<p>' + item.title + '</p>';

					li += '<div class="diagramListBtn">';

					li += '<span class="RlistFrom nowrap">' + item.publisher + '</span>';

					li += '<span class="RlistTime">' + item.publishDate + '</span>';

					li += '</div>';

					li += '</li>';

					li += '</a>';

				});

				$('#relatedBidNotice').append(li);

			}

			

			//加载相关项目

			var relatedProject = result.data[2].relatedProject;

			li = '';

			if (relatedProject == null || relatedProject.length == 0) {

				li += '<li class="no-data">';

				li += '暂无';

				li += '</li>';

				$('#relatedProject').append(li);

			}else{

				$.each(relatedProject, function (i, item){

					li += '<li class="pointer">';

					li += '<p onclick="toProjectInfo(\''+item.id+'\');">' + item.title + '</p>';

					li += '<div class="diagramListBtn">';

					li += '<span class="RlistFrom nowrap">' + item.publisher + '</span>';

					li += '<span class="RlistTime">' + item.publishDate + '</span>';

					li += '</div>';

					li += '</li>';

				});

				$('#relatedProject').append(li);

			}

			//相关公示

			var relatedProjectPublicity = result.data[5].relatedProjectPublicity;

			li = '';

			if (relatedProjectPublicity == null || relatedProjectPublicity.length == 0) {

				li += '<li class="no-data">';

				li += '暂无';

				li += '</li>';

				$('#relatedProjectPublicity').append(li);

			}else{

				$.each(relatedProjectPublicity, function (i, item){

					li += '<a href="javascipt:void(0)" onclick="toPublicity(\'' + item.id + '\');">';

					li += '<li>';

					li += '<p>' + item.title + '</p>';

					li += '<div class="diagramListBtn">';

					li += '<span class="RlistFrom nowrap">' + item.publisher + '</span>';

					li += '<span class="RlistTime">' + item.publishDate + '</span>';

					li += '</div>';

					li += '</li>';

					li += '</a>';

				});

				$('#relatedProjectPublicity').append(li);

			}

		}

	});

}



//获取相关咨询

function getRelatedConsult() {

	var url = '/sqzc/m/cms/consult/getConsultRelated';

	var data = {'pageNumber': 1,'pageSize': 2, 'title': '深圳市财政局 深圳市人力资源和社会保障局 深圳市科技创新局 国家税务总局深圳市税务局关于印发《深圳市粤港澳大湾区个人所得税优惠政策2024纳税年度财政补贴申报指南》的通知'};

	common.ajaxFunc(url,data,'json',function(result){

		if (result.success) {

			var list = result.data;

			var li = '';

			if (list == null || list.length == 0) {

				li += '<li class="no-data">';

				li += '暂无';

				li += '</li>';

				$('#relatedConsult').append(li);

				return;

			}

			$.each(list, function (i, item){

				li += '<li>';

				li += '<div class="questionsCol">';

				li += '<span class="questionsType"><img src="/sqzc/images/askImg.png" alt=""></span>';

				li += '<p>'+ item.consult_content +'</p>';

				li += '</div>';

				li += '<div class="questionsCol">';

				li += '<span class="questionsType answers"><img src="/sqzc/images/answers.png" alt=""></span>';

				if (item.reply_content != null && item.reply_content != '') {

					li += '<p>'+ item.reply_content +'</p>';

				} else {

					li += '<p>&nbsp;</p>';

				}

				li += '</div>';

				li += '<div class="diagramListBtn questionsColBtn">';

				li += '<span class="RlistFrom nowrap">咨询者：<i class="asker"></i>'+ item.consultant_name +'</span>';

				li += '<span class="RlistTime">'+ item.consult_timeStr +'</span>';

				li += '</div>';

				li += '</li>';

			});

			$('#relatedConsult').append(li);

		}

	});

}

// 到项目公示和申报通知

function toPublicity(id) {

	window.open('/sqzc/m/cms/publicity/publicityInfo/'+id);

}

// 到项目

function toProject(id) {

	window.open('/sqzc/m/cms/project/toProjectInfo/'+id);

}

// 到政策解读

function toPolicyAnalysis(id) {

	window.open('/sqzc/m/cms/policyanalysis/policyanalysisinfo/'+id);

}

// 到政策

function toPolicy(id) {

	window.open('/sqzc/m/cms/policy/policyinfo/'+id);

}

//检查登录

	function checkLogin(){

			var isLogin = 'false';

			console.log(isLogin)

			if(isLogin == 'true'){

				return true;

			}else{

				var msg = '请你先登录系统!';

				layer.msg(msg,null,null);

				return false;

			}

		}

// 留言

function leaveMessage() {

	var msg = '请你先登录系统';

	if(!checkLogin()){

		layer.msg(msg,null,null);

		return;

	}else{

		msg = '功能建设中，暂未开放';

		layer.msg(msg,null,null);

		return;

	}

	var url = '/sqzc/m/cms/consult/toLeaveMessage?id=nfsheqigsxx12203332&title=深圳市财政局 深圳市人力资源和社会保障局 深圳市科技创新局 国家税务总局深圳市税务局关于印发《深圳市粤港澳大湾区个人所得税优惠政策2024纳税年度财政补贴申报指南》的通知&type=&dept_name=深圳市财政局';

	url = encodeURI(encodeURI(url.replace(/%/g,'%25')));

	window.open(url);

}

// 更多咨询

function toAskAnswers() {

	var url = '/sqzc/m/cms/consult/toAskAnswers?title=深圳市财政局 深圳市人力资源和社会保障局 深圳市科技创新局 国家税务总局深圳市税务局关于印发《深圳市粤港澳大湾区个人所得税优惠政策2024纳税年度财政补贴申报指南》的通知';

	url = encodeURI(encodeURI(url.replace(/%/g,'%25')));

	window.open(url);

}

//查询是否收藏

function getCollectStatus() {

	var url = '/sqzc/m/pf/collect/getCollect';

	var data = {'collect_id':'nfsheqigsxx12203332'};

	common.ajaxFunc(url,data,'json',function(result){

		if (result.success) {

			var flag = result.data;

			var text = '';

			if (flag) {

				text = '<i class="iconfont beCollectIcon">&#xe747;</i> 已收藏';

				$('.beCollect').attr('collectstatus','has');

			} else {

				text = '<i class="iconfont beCollectIcon">&#xe748;</i> 收藏';

				$('.beCollect').attr('collectstatus','not');

			}

			$('.beCollect').append(text);

		}

	});

}

// 收藏或取消收藏

function saveOrCancelCollect() {

	if(!checkLogin()){

		return;

	}

	var url = '/sqzc/m/pf/collect/saveOrCancelCollect';

	var collectstatus = $('.beCollect').attr('collectstatus');

	var type = '';

	var res;

	if (collectstatus == 'not') {

		type = 'save';

		res = '{';

	}

	if (collectstatus == 'has') {

		type = 'cancel';

	}

	var original_url = window.location.href;

	var data = {'collect.collect_title': '深圳市财政局 深圳市人力资源和社会保障局 深圳市科技创新局 国家税务总局深圳市税务局关于印发《深圳市粤港澳大湾区个人所得税优惠政策2024纳税年度财政补贴申报指南》的通知','collect.collect_id':'nfsheqigsxx12203332',

			'collect.original_url':original_url,'collect.collect_type':'informationdisclosure',

			'collect.publish_date':'2025-05-30','type':type,'data':res};

	common.ajaxFunc(url,data,'json',function(result){

		if (result.success) {

			$('.beCollect').empty();

			var flag = result.data;

			if (flag) {

				text = '<i class="iconfont beCollectIcon">&#xe747;</i> 已收藏';

				$('.beCollect').attr('collectstatus','has');

				$('.beCollect').append(text);

				layer.msg("收藏成功", null, null);

			} else {

				text = '<i class="iconfont beCollectIcon">&#xe748;</i> 收藏';

				$('.beCollect').attr('collectstatus','not');

				$('.beCollect').append(text);

				layer.msg("取消成功", null, null);

			}

		} else {

			if(type =='save'){

				layer.msg("收藏失败", null, null);

			}else if(type =='cancel'){

				layer.msg("取消失败", null, null);

			}

		}

	});

}

</script>

</body>

</html>