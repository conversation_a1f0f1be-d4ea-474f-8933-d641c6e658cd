<html class=" js no-mobile desktop no-ie chrome chrome114 sqzc-section m-section cms-section policy-section policyinfo-section w-1907 gt-240 gt-320 gt-480 gt-640 gt-768 gt-800 gt-1024 gt-1280 gt-1440 gt-1680 lt-1920 no-portrait landscape gradient rgba opacity textshadow multiplebgs boxshadow borderimage borderradius cssreflections csstransforms csstransitions no-touch retina fontface domloaded" id="nfsheqizcyw12220432-page"><head>
<meta name="SiteName" content="广东涉企政策发布平台">
<meta name="SiteDomain" content="sqzc.gd.gov.cn">
<meta name="SiteIDCode" content="4400000131">
<meta name="ColumnName" content="政策库">
<meta name="ColumnDescription" content="广东省涉企政策发布平台政策库栏目发布广东省和各地市制定的涉企法规和政策文件等。提供以指定条件和任意关键词检索相关政策的查询功能。">
<meta name="ColumnKeywords" content="广东省文件,政策文件,涉企文件,涉企政策文件">
<meta name="ColumnType" content="政策库">
<meta name="ArticleTitle" content="罗湖区投资促进局局关于印发《深圳市罗湖区产业发展专项资金管理办法》的通知">
<meta name="PubDate" content="2025-06-10 16:17">
<meta name="ContentSource" content="罗湖区投资促进局">
<title>罗湖区投资促进局局关于印发《深圳市罗湖区产业发展专项资金管理办法》的通知</title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0">
	<meta name="apple-mobile-web-app-capable" content="yes">
	<meta name="apple-mobile-web-app-status-bar-style" content="black">
	<meta name="format-detection" content="telephone=no">
	<meta data-n-head="true" data-hid="description" name="description" content="广东省涉企政策发布平台 - 发布广东省和各地市制定的涉企法规和政策文件、相关的解读以及各类政策专题。提供以关键词检索相关政策、解读、项目和公示的查询功能。">
	<meta data-n-head="true" data-hid="keyword" name="keyword" content="广东涉企政策发布平台,涉企政策一站式服务,粤企政策通,涉企政策">
	<meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">

<link rel="stylesheet" type="text/css" href="/sqzc/js/layui/css/layui.css?v=20211025" media="all">

<link rel="stylesheet" type="text/css" href="/sqzc/css/common.css?v=20211025">
<link rel="stylesheet" type="text/css" href="/sqzc/css/style.css?v=20211025">
<link rel="stylesheet" type="text/css" href="/sqzc/css/cover.css?v=20211025&amp;t=1.00">
<link rel="stylesheet" type="text/css" href="/sqzc/css/fonts/iconfont.css?v=20211025&amp;t=1.00">
<link rel="shortcut icon" type="image/png" href="/sqzc/images/logo.png" sizes="16x16">
<script src="/sqzc/js/jquery/jQuery-2.1.3.min.js" type="text/javascript" charset="utf-8"></script>


	<link rel="stylesheet" type="text/css" href="/sqzc/css/policyinfocss.css?v=20211025">
<link id="layuicss-layer" rel="stylesheet" href="https://sqzc.gd.gov.cn/sqzc/js/layui/css/modules/layer/default/layer.css?v=3.1.1" media="all"></head>
<body>
<style>
	.relatedAccessory,.diagramRightList {
		font-size:16px;
	}
	
	a#jumpUrl:hover{
		color:#3E97DF;
	}
	
</style>

<!--头部-->
<script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.3.2.js"></script>
<script>
	wx.miniProgram.navigateBack({url: '/pages/home/<USER>'})
	wx.miniProgram.postMessage({ data: {msg: '' }});
</script>
<script type="text/javascript" src="https://cdn.staticfile.org/jquery-cookie/1.4.1/jquery.cookie.min.js"></script>
<!-- <script type="text/javascript" src="//api.map.baidu.com/getscript?v=3.0&ak=L6wio39Dee0SixbufOFPnMCjTvTicZMP&s=1"></script> -->

<style>
.newPhoneBg{
	background: url('/sqzc/images/newHomeImg/bj-360.png') no-repeat center top;
}
/*热搜词-开始*/
.hotwords {
	width: 492px;
}
.hotwords div {
	float: left;
	display: inline-block;
	font-size: 14px;
	color: white;
	text-shadow: 3px 3px 12px black;
}
.hotwords div:last-child {
    width: 85%;
}
.hotwords span {
	/*  padding-right: 10px; */
	 cursor: pointer;
	 padding:0px 5px 0px 5px;
}
.hotwords span:hover {
	 /* color: #3E97DF; */
     /* text-decoration: underline; */
     background-color:#688fae;
}
/*热搜词-结束*/
</style>
<div class="gvmHeader zcjdHeaher ">
    
    
    
   
        <!--头部logo栏-->
            	<!--头部logo栏-->
        <div class="header-title">
	        <div class="header-title-cont">
	            <div class="platformName">
	            	<a href="http://www.gd.gov.cn/" target="_blank">
	            		<img src="/sqzc/images/newHomeImg/国徽1024.png" style="margin-top: 18px;width:40px;position: absolute;">
	                    <img src="/sqzc/images/newHomeImg/<EMAIL>" style="margin-top: -10px;width:180px;">
	                </a>
	                <a href="/sqzc/m/home/<USER>">
	                    <!-- <img src="/sqzc/images/newHomeImg/newLogo-2.png" alt="" class="logoImg"> -->
	                    <img src="/sqzc/images/newHomeImg/logo.png" style="margin-top: -10px;">
	                    		<span style="display: inline-block;width:135px;">粤企政策通</span>
	                    <i class="middleLine" style="height:20px;top:0px;"></i>
	                </a>
		                <a href="javascript:void(0);"><span class="zwfwText">广东省</span></a>
	                	<div id="changeCity">
		                	<span>切换地区<img src="/sqzc/images/jt1.png"></span>
	                	</div>
	            </div>
	            <form action="" class="layui-form " onsubmit="return false;">
	                    <input type="text" placeholder="" class="layui-input searchInput" id="searchButton" onkeyup="common.onKeyEnter(event,function(){toCmsSearchList2();});">
	                <span class="iconfont searchIcon" onclick="toCmsSearchList2();"></span>
	            </form>
	                   <div class="header-title-login">       
            <span class="LoginType">
                    <i class="qywxLogin">
						<img src="/sqzc/images/qywx.png" alt="">
						<div class="appCodeDiv none">
	                   		<img class="appCode" src="/sqzc/images/qywxapp.jpg">
	                   		<img class="codePng" src="/sqzc/images/qr.png">
	                   		<span style="font-size:15px">粤企政策通-企业微信</span>
                   		</div>
					</i>                   
                   
                   	<i class="wechatLogin">
                   		<img src="/sqzc/images/wx.png" alt="">
                   		<div class="appCodeDiv none">
	                   		<img class="appCode" src="/sqzc/images/wxapp.jpg">
	                   		<img class="codePng" src="/sqzc/images/qr.png">
	                   		<span>粤企政策通-小程序</span>
                   		</div>
                   	</i>  
                     <i class="middleLine"></i>
                </span>
                
                <div class="loginText">
                       <span class="loginBefore" onclick="login();">登录</span>
                </div>
            </div> 
            

		        <div class="cityBox none">
	            	<div style="cursor: pointer;" class="city canUsed">广东省 (全省政策)</div>
	            	<ul><li class="city canUsed">广州市</li><li class="city canUsed">深圳市</li><li class="city canUsed">珠海市</li><li class="city canUsed">汕头市</li><li class="city canUsed">佛山市</li><li class="city canUsed">韶关市</li><li class="city canUsed">河源市</li><li class="city canUsed">梅州市</li><li class="city canUsed">惠州市</li><li class="city canUsed">汕尾市</li><li class="city canUsed">东莞市</li><li class="city canUsed">中山市</li><li class="city canUsed">江门市</li><li class="city canUsed">阳江市</li><li class="city canUsed">湛江市</li><li class="city canUsed">茂名市</li><li class="city canUsed">肇庆市</li><li class="city canUsed">清远市</li><li class="city canUsed">潮州市</li><li class="city canUsed">揭阳市</li><li class="city canUsed">云浮市</li></ul>
	            </div>
	        </div>
	        
	    </div>
        <!--头部logo栏-->

        <!--头部logo栏-->
        
				    <!-- <div class="headLinkTab notIndexHead pcHeadForm">
		        <ul id="_ul_nav">
		            
		        </ul>
		    </div> -->
		    <form action="" class="layui-form newPhoneHead" onsubmit="return false;">
              		
				  <div class="nav headLinkTab notIndexHead">
				    <ul class="navfouce" id="_ul_nav">
				      	<li><a href="/sqzc/m/home/<USER>">首页<i class="linkTabLine"></i></a></li>
			            <li class="presentPage activeLi"><a href="/sqzc/m/cms/policy">政策库 <i class="linkTabLine"></i></a></li>
			            <li><a href="/sqzc/m/cms/policyanalysis">解读库 <i class="linkTabLine"></i></a></li>
			            <li><a href="/sqzc/m/cms/project">报项目<i class="linkTabLine"></i></a></li>
			            <li><a href="/sqzc/m/cms/publicity">查结果<i class="linkTabLine"></i></a></li>
			            <li><a href="/sqzc/m/cms/match">精准搜<i class="linkTabLine"></i></a></li>
			            <!-- <li><a href="/sqzc/m/cms/solicitnouns/consultAndSolicitnouns" >互动交流<i class="linkTabLine"></i></a></li>
			            <li><a href="/sqzc/m/cms/solicitnouns" >意见征求<i class="linkTabLine"></i></a></li> -->
			            <li class="jiaoliuTab"><a href="javascript:void(0);" onclick="toConsultAndSolicitnouns();">互动交流<i class="linkTabLine"></i> </a></li>
			            <li class="redianTab"><a href="http://sqzc.gd.gov.cn/rdzt">热点专题</a></li>
				    </ul>
				  </div>
 			<!--头部tab导航栏-->
        </form>
		    <!--头部搜索栏-->

    
    
    
</div>


<script type="text/javascript">
	
  /* 
  该段代码在common.js文件中，用于判断用户是否已经登录，登录后需重新刷新页面
  $(function(){
	  
	  var userCode=GetUrlParam("code") || "";
	  var isReload = GetUrlParam("isReload") || "";
	  //alert(userCode+"-"+isReload);
	  if(userCode!="" && isReload!="1")
	  {
		  //alert(window.location.href);
		  window.location.href= window.location.href+"&isReload=1";  
	  }
	  
  }); */
  $(function(){
	  //判断是哪个菜单
	  var currUrl = window.location.pathname;  //只获取前面部分，不包括http
	  /* if(currUrl.indexOf("project")>-1){//项目申报与信息公示合并
		  currUrl = currUrl.replace("project","publicity");
	  } */
	  var lis = $("#_ul_nav li");
	  var li = null
	  var url = null;
	  for(var i=0;i<lis.length;i++)
	  {
		  li = $(lis[i]);
		  url = li.find("a").attr("href");	
		  click=li.find("a").attr("onclick");
		  if(currUrl==url || currUrl.indexOf(url+"/")>-1||typeof(click)!="undefined") //存在，加上斜杆防止类似
		  {
			  li.addClass("presentPage activeLi");
			  break;  //终止循环
		  }
	  }
	  
  });
  
  //跳转到查询列表
  function toCmsSearchList()
  {
	var searchText = $("#searchText").val();
	var url = '/sqzc/m/cms/search?searchText='+searchText;
	url = encodeURI(encodeURI(url.replace(/%/g,'%25')));
	if(browserRedirect()){
		window.location.href = url;
	}else{
		common.win_open(url);
	}
	  
  }
  
  //跳转到查询列表
  function toCmsSearchList2()
  {
	var searchText = $("#searchButton").val();
	var url = '/sqzc/m/cms/search?searchText='+searchText;
	url = encodeURI(encodeURI(url.replace(/%/g,'%25')));
	if(browserRedirect()){
		window.location.href = url;
	}else{
		common.win_open(url);
	}
	  
  }
  
</script>
<!--广东省切换-->


<script type="text/javascript">

function login(){
	//登陆
	//function(url, data, dataType, callback,ecallback,ajaxOptions);
	var headurl="https://tyrz.gdbs.gov.cn/am/login/initAuth.do?gotoUrl=";
	var gotourl="https://tyrz.gd.gov.cn/tif/sso/connect/page/oauth2/authorize?service=initService"
			+"&response_type=code&scope=all"
			+"&client_id=gdbssqzc&client_secret=sq20190130"
			+"&redirect_uri=";
	var redirecturl="https://sqzc.gd.gov.cn/sqzc/m/home/<USER>"; //需要修改为返回的地址
	//var redirecturl = "http://127.0.0.1:8080/index"; //测试用
	//var selfurl=headurl+encodeURIComponent(gotourl+encodeURIComponent(redirecturl));
	var selfurl=gotourl+encodeURIComponent(redirecturl);
	location.href=selfurl;
	//window.open(selfurl);
	
    /* common.ajaxFunc("/sqzc/m/pf/login/userLogin",{},"json",function(result){
    	if(result.success){
    		location.reload();
    	}
    }); */
}

function logout()
{
	layui.use(['layer'], function(){
		//注销
		layer.confirm('确定退出？', {
			  btn: ['确定','取消'] //按钮
			}, function(){
			  
				common.ajaxFunc("/sqzc/m/pf/login/logout",{},"json",function(result){
					if(result.success){
						//location.reload();
						
						//window.location.href="/sqzc/m/home/<USER>";
						window.location.href="http://tyrz.gd.gov.cn/_tif_sso_logout_/?redirect_uri=https%3A%2F%2Fsqzc.gd.gov.cn%2Fsqzc%2Fm%2Fhome%2Findex";
					}
				});  
				
			}, function(){  //取消
			  
		});
		
	});
	
	
	
}
//分站访问+1
function addChildWebReadCount(webCity){
	var city = webCity;
	var params = {};
	var appCode = "pc";
	if(browserRedirect()){
		appCode = "mini";
	}
	console.log(appCode);
	if(city){
		params = {'city':city,'appCode':appCode};
	}else{
		params = {'city':'广东省','appCode':appCode};
	}
	var url = "/sqzc/m/cms/common/accessCount";
	common.ajaxFunc(url,params,"json",function(result){
		//console.log(result);
		if (result.success) {
			//console.log(result.data);
			getVistiCount();
		}
	});
}
//获取城市
function getHeadCity() {
	var url = '/sqzc/m/pf/city/getList';
	var data = '';
	common.ajaxFunc(url,data,'json',function(result){
		if (result.success) {
			var list = result.data[0];
			////console.log(list);
			var html = "";
			for(var i=0;i<list.length;i++){
				var city = list[i].city_name;
				var open_status = list[i].open_status;
				if(city == "广东省"){
					continue;
				}
				if(open_status==1){
					/* $(".city").each(function(n,item){
						if($(item).text()==city){
							$(item).addClass("canUsed");
						}
					}) */
					html += '<li class="city canUsed">'+city+'</li>';
				}else{
					html += '<li class="city">'+city+'</li>';
				}
			}
			$(".cityBox ul").empty();
			$(".cityBox ul").html(html);
		}
	});
}
function getCityCookie(name){
	/* var value = unescape($.cookie(name)?$.cookie(name):""); */
	var value = getCookie("cityRecord");
	return value;
}
//登陆后个人中心、工作台跳转
function personalCenter(url)
{
	if(browserRedirect()){
		  window.location.href = url;
	}else{
		 window.open(url);
	}
}
//判断pc端还是手机端
var isPhone = false;
if(browserRedirect()){
	  $('.phoneHeadForm').show();
	  $('.pcHeadForm,.header-title-cont,.service').hide();
	  $('.newPhoneHead').addClass('newPhoneBg');
}else{
	  $('.phoneHeadForm,.dibutangchu').remove();
	  $('.pcHeadForm,.header-title-cont,.service').show();
}
var region = "广东省";
function getlocationByAPI(flag){
	//怀疑下面的百度API会导致首页加载缓慢，因此先注释掉
	//var map = new BMap.Map("container");
    //var nowCity = new BMap.LocalCity();
    //nowCity.get(bdGetPosition);
    
    //怀疑下面的百度API会导致首页加载缓慢，因此先注释掉，将定位默认为广州市
    setCookie("visitPlace",decodeURI('广州市','utf-8'));
    if(!hadCount){
    	counter(region);//访问量、访问人次统计
    	hadCount = true;
    }
    if(flag){
    	afterChangeWebCity();
    }
    
    function bdGetPosition(result){
    	console.log("result="+result);
		// if(result.name != null &&)
		//console.log("result.name"=+);
    	var cityName = result.name; //当前的城市名
		var lat;
		var lng;
		try {
			lat = result.center.lat;
    		lng = result.center.lng;
		} catch (error) {
			lat = 23.13533631;
			lng = 113.27143134;
		}

    	//按经纬度获取省份
   //  	$.ajax({
   //          type: "POST",
   //          dataType: "jsonp",
   //          url: '//api.map.baidu.com/reverse_geocoding/v3/?ak=L6wio39Dee0SixbufOFPnMCjTvTicZMP&callback=renderReverse&location=' + lat + ',' + lng + '&output=json',
   //          success: function (response) {
   //          	//console.log(response);
   //              if (response.status == 0) {
   //                  var province = response.result.addressComponent.province;
   //                  if(province == '广东省'){
   //                  	//region = cityName;
                    	
	  //                  	 var nowTime = new Date().getTime();
	  //                  	 var positionTime = getCookie("positionTime");
	  //                  	var position = getCookie("sq_position");
	  //                  	 //定位有效设置1天
	  //                  	 var effectiveTime = 24 * 60 * 60 * 1000;//(24 * 60 * 60 * 1000)
	  //                  	 //console.log(nowTime - positionTime);
	  //                  	setCookie("visitPlace",decodeURI(cityName,'utf-8'));
	  //                  	 /* if(position != 'used' || nowTime - positionTime > effectiveTime){
	  //                  		setCookie("sq_position","used");
   //                     	 	setCookie("positionTime",nowTime);
	  //                      	setCookie("webCity",decodeURI(region));
   //         					$.cookie("cityRecord",escape(region),{ expires: 7 });
	  //                  	 }else if(position == 'used' && nowTime - positionTime < effectiveTime){
	  //                  		 console.log('1日内已定位过，现以webCity设置子站');
	  //                   	//setCookie("positionTime",nowTime);
	  //                   	region = getCookie("webCity");
	  //                   	if(region && region.indexOf('%u') != -1){
	  //                   		region = unescape(region);
		 //                   	 }
		 //                   	 if(region && region!=""){
		                   		  
	  //                        }else{
	  //                       	 region = getCityCookie("cityRecord");
	  //                      	 	if(region && region.indexOf('%u') != -1){
	  //                      	 	region = unescape(region);
	  //                      	 	}
	  //                      	  	setCookie("webCity",region);
	  //                        }
	  //                  	 } */
	  //                  	 //console.log(region);
   //                  }
   //             	    if(!hadCount){
   //              		counter(region);//访问量、访问人次统计
   //              		hadCount = true;
   //             	    }
	  //              	 /* var zwfwText= $('.zwfwText').text();
	  //              	 if (zwfwText=="标签查询"){
	  //              		 $('#changeCityLable').removeClass('none');
	  //              	 }  
   //                	//addChildWebReadCount(webCity);//访问人次+1
   //             	    if (zwfwText!="政策匹配分析" && zwfwText!="工作台" && region){
   //               	  	$('.zwfwText').text(region);
   //               	  	$('.xuanze-qy[tapmode]').text(region);
   //             	    }
   //                 	$('.cityBox .city').each(function(n,item){
   //  					$(item).removeClass("beSelected");
   //                 		if($(item).text() == region){
   //                 			$(item).addClass("beSelected");
   //                 		}
   //  				})
   //     				$('.zwfwText').text(region); */
   //     				if(flag){
   //      				afterChangeWebCity();
   //     				}
   //              }
			// 	else {
			// 		if(!hadCount){
			// 			counter(region);//访问量、访问人次统计
			// 			hadCount = true;
			// 		}
			// 		if(flag){
			// 			afterChangeWebCity();
			// 		}
			// 	}
   //          },
			// error: function (response) {
			// 	console.log("response.status="+response.status);
			// 	if(!hadCount){
			// 		counter(region);//访问量、访问人次统计
			// 		hadCount = true;
			// 	}
			// 	if(flag){
			// 		afterChangeWebCity();
			// 	}
			// }  
   //      });
   }
}

function isNull(obj) {
	if(typeof obj == "undefined" || obj == null || obj == ""){
		return true;
	}
	return false;
}

var hadCount = false;
$(function () {
		getlocationByAPI();
		setCookie("webCity",'');
	  getHotword(); 
	  getHeadCity();
	  
    //模拟点击登录按钮静态操作
    /* $('.loginBefore').click(function () {
    	var tal ='<i class="layui-icon">&#xe667;</i><i class="middleLine"></i>';
        $(this).hide().next('.beLogin').show();
        $('.LoginType').html(tal); 
    });*/
    $('.beLogin').click(function () {
    	$(this).next('.personalLink').toggleClass('showBlock');
    }); 
    
    
    $(document).on("click",function(e){
        var e = e || window.event; //浏览器兼容性
        var elem = e.target || e.srcElement;
        while (elem) { //循环判断至跟节点，防止点击的是div子元素
            if (elem.id && elem.id == 'beLogin') {
                return;
            }
            elem = elem.parentNode;
        }
        if ($('.personalLink').hasClass('showBlock')){
            $('.personalLink').removeClass('showBlock');
        }
    });
    //            模拟点击登录按钮静态操作----end

    //            模拟点击退出登录静态操作
    $('.logout').click(function () {
        var tal ='<i class="qqLogin"></i> <i class="wechatLogin"></i> <i class="middleLine"></i>';
        $('.loginBefore').show().next('.beLogin').hide();
        $('.LoginType').html(tal);
    });
	$('#changeCity').click(function(){
		$('.cityBox').toggleClass('none');
		if ($('.cityBox').hasClass('none')){
			$('#changeCity').html('<span>切换地区<img src="/sqzc/images/jt1.png"/></span>');
		}else{
			$('#changeCity').html('<span>切换地区<img src="/sqzc/images/jt2.png"/></span>');
		}
	});
	//切换分站(地区)
	$('.cityBox').on('click','.city',function(){
		$('.cityBox').addClass('none');
		var city = $(this).text();
		if(city && city.indexOf('广东省') != -1){
			city = '广东省';
		}
		if($(this).hasClass("canUsed")){
			var oldText = $('.zwfwText').text();
			if(city != oldText){
				counter(city);
				//addChildWebReadCount(city);
				$('.cityBox .city').each(function(n,item){
					$(item).removeClass("beSelected");
				})
				$(this).addClass("beSelected");
				$('.zwfwText').text(city);
				//console.log(city)
				setCookie("webCity",decodeURI(city));
				$.cookie("cityRecord",escape(city),{ expires: 7 });
				afterChangeWebCity();
			}
		}
	});
	$('.cityBox .city').each(function(n,item){
		$(item).removeClass("beSelected");
		if($(item).text() == $('.zwfwText').text()){
			$(item).addClass("beSelected");
		}
	})
	$('.tc-btn ul li ').on('click','a',function(){
		$(".tc-btn ul li a").each(function(n,item){
			$(item).removeClass("btn-active");
		})
		var that = $(this);
		that.addClass("btn-active");
		var city = $(this).text();
		var oldText = $('.xuanze-qy').text();
		if(city && city.indexOf('广东省') != -1){
			city = '广东省';
		}
		if(city != oldText){
			counter(city);
			//addChildWebReadCount(city);
			$('.cityBox .city').each(function(n,item){
				$(item).removeClass("beSelected");
			})
			$(this).addClass("beSelected");
			$('.xuanze-qy').text(city);
			//console.log(city)
			setCookie("webCity",decodeURI(city));
			$.cookie("cityRecord",escape(city),{ expires: 7 });
			afterChangeWebCity();
		}
		popup.hide(document.getElementById("bottom"));
	})
	
	$(document).click(function(e){
		var text1 =$(e.target).text();
		if(!$('.cityBox').hasClass('none') && text1 !='切换地区'){
			var text2 =$(e.target).attr('class') +'';
			var text3=$(e.target).parents('div').attr('class')+'';
			if(text2.indexOf('cityBox') < 0 && text3.indexOf('cityBox') < 0){
				$('.cityBox').addClass('none');
				$('#changeCity').html('<span>切换地区<img src="/sqzc/images/jt1.png"/></span>');
			}
		}
		if(!$(e.target).hasClass('wechatLogin') && !$(e.target).parent('i').hasClass('wechatLogin') ){
			$('.wechatLogin .appCodeDiv').addClass('none');
		}
		if(!$(e.target).hasClass('qywxLogin') && !$(e.target).parent('i').hasClass('qywxLogin') ){
			$('.qywxLogin .appCodeDiv').addClass('none');
		}
	})
	$('.wechatLogin').click(function(){
		$('.wechatLogin .appCodeDiv').toggleClass('none');
	})
	$('.qywxLogin').click(function(){
		$('.qywxLogin .appCodeDiv').toggleClass('none');
	})
	
	$('.phoneHeadForm .suosou').hover(function(){
		$('.phoneHotwords').show();
	},function(){
		$('.phoneHotwords').hide();
	})
});
//
function toConsultAndSolicitnouns(){
	/* 
	if(!checkLogin()){
		return;
	} */
	var url = "/sqzc/m/cms/solicitnouns/consultAndSolicitnouns";
	//判断pc端还是手机端
	if(browserRedirect()){
		 $('.jiaoliuTab a').removeAttr('onclick').attr('href',url);
	}else{
		url = encodeURI(encodeURI(url.replace(/%/g,'%25')));
		window.open(url);
	}
	
}
function browserRedirect() {
    var sUserAgent = navigator.userAgent.toLowerCase();
    var bIsIpad = sUserAgent.match(/ipad/i) == "ipad";
    var bIsIphoneOs = sUserAgent.match(/iphone os/i) == "iphone os";
    var bIsMidp = sUserAgent.match(/midp/i) == "midp";
    var bIsUc7 = sUserAgent.match(/rv:*******/i) == "rv:*******";
    var bIsUc = sUserAgent.match(/ucweb/i) == "ucweb";
    var bIsAndroid = sUserAgent.match(/android/i) == "android";
    var bIsCE = sUserAgent.match(/windows ce/i) == "windows ce";
    var bIsWM = sUserAgent.match(/windows mobile/i) == "windows mobile";
    if (bIsIpad || bIsIphoneOs || bIsMidp || bIsUc7 || bIsUc || bIsAndroid || bIsCE || bIsWM) {
    //跳转移动端页面
    	return isPhone = true;
    } else {
    //跳转pc端页面
    	return isPhone = false;
    }
}

function getHotword() {
	var url = '/sqzc/m/cms/common/getTopViewLabelList';
	var data = '';
	common.ajaxFunc(url,data,'json',function(result){
		if (result.success) {
			console.info(result.data);
			if(result.data!=null && result.data.length>0) {
				if(browserRedirect()){
					var html = "<div>热搜词：</div><div>";
					for (var i = 0; i < result.data.length; i++) {
						html += "<span>"+result.data[i].label+"</span>";
					}
					html += "</div>";
					$(".phoneHotwords").empty();
					$(".phoneHotwords").html(html);
					$(".phoneHotwords div:last span").click(function(){
						searchingByHotword(this);
					});
				}else{
					var html = "<div>热搜词：</div><div>";
					for (var i = 0; i < result.data.length; i++) {
						html += "<span>"+result.data[i].label+"</span>";
					}
					html += "</div>";
					
					$(".hotwords").empty();
					$(".hotwords").html(html);
					
					$(".hotwords div:last span").click(function(){
						searchingByHotword(this);
					});
				}
			}
		}
	});
}

//搜索按钮(按热词)
function searchingByHotword(obj){
	var hotword = $(obj).text();
	/* var searchTextObj = $(obj).parents().find("#searchText");
	searchTextObj.val(hotword);
	//alert(searchTextObj.val());
	searchTextObj.next().click(); */
	
	if(hotword == null || hotword.trim() == ""){
		return;
	}
	var url = '/sqzc/m/cms/search?searchText='+hotword;
	url = encodeURI(encodeURI(url.replace(/%/g,'%25')));
	if(browserRedirect()){
		window.location.href = url;
	}else{
		window.open(url);
	}
}
//访问量与访问人次统计
function counter(webCity){
	var appCode = "pc";
	if(browserRedirect()){
		appCode = "mini";
	}
	var url = '/sqzc/m/cms/common/counter';
	common.ajaxFunc(url,{'city':webCity,'appCode':appCode},'json',function(result){
		if (result.success) {
			console.info(result.data);
		}
	});
}
</script>
<!-- 正文start  -->
<!--主体内容-->
<div class="indexHome-cont">
    <!--    标题-==============================-->
    <div class="jdxqTitle" style="display:none;">
    	<span><img alt="" src="/sqzc/images/position.png"></span>
        <span>找政策</span>
        <span class="layui-icon">—</span>
        <span>
            <i><span id="policytitle2" title="罗湖区投资促进局局关于印发《深圳市罗湖区产业发展专项资金管理办法》的通知">罗湖区投资促进局局关于印发《深圳市罗湖区产业发展专项资金管理办法》的通知</span></i>
        </span>
    </div>
    <!--    标题-==============================-->

    <!--政策原文-->
    <div class="diagram">
       	<!-- 顶部标题 -->
       	<div class="contentTop">
	       	<div class="top_title">罗湖区投资促进局局关于印发《深圳市罗湖区产业发展专项资金管理办法》的通知</div>
	     	<div>
	       		<!-- <div class="policyAbstract">
	       			摘要
				</div> -->
				<div class="policyAbstractCont" id="abstractText" title=""></div>	
	        </div>	        
            <div class="zcLable" id="zcLabel">
                    <!-- <span class="beCheckedSpan">制造业</span>
                    <span>装备</span>
                    <span>研发</span>
                    <span>服务</span>
                    <span>投资</span>
                    <span>工业</span>
                    <span>高薪技术</span>
                    <span>互联网</span>
                    <span>大数据</span> -->
            <span>财税支持</span><span>财政奖补</span><span>重工业</span><span>产业扶持</span><span>投融资</span><span>产业资助</span><span>资金申报</span><span>投资引导</span><span>转型升级</span><span>其他未列明行业</span><span>资金管理办法</span></div>
        </div>
        <!-- 政策标签 -->
        <div class="diagramLeft" style="width: 790px;">
        	<div class="layui-tab xmsbCard layui-tab-brief" lay-filter="docDemoTabBrief">
        		<ul class="layui-tab-title">
                    <li class="layui-this" onclick="getCollectStatus()"><span>政策原文</span></li>
                    <li onclick="loadRelatedAnalysisInfo();getCollectStatus2();" class="relatedAnalysis" style="display: none;"><span>政策解读</span></li>
                    <div class="navRightPart" style="top: 10px;line-height: 55px;">
                    <span class="matching">匹配度：<i>70%</i></span>
                    <a class="noteIcon" id="jumpUrl" href="http://hdjl.gd.gov.cn/jump/sqzx?site_id=755527&amp;zxzc=%E7%BD%97%E6%B9%96%E5%8C%BA%E6%8A%95%E8%B5%84%E4%BF%83%E8%BF%9B%E5%B1%80%E5%B1%80%E5%85%B3%E4%BA%8E%E5%8D%B0%E5%8F%91%E3%80%8A%E6%B7%B1%E5%9C%B3%E5%B8%82%E7%BD%97%E6%B9%96%E5%8C%BA%E4%BA%A7%E4%B8%9A%E5%8F%91%E5%B1%95%E4%B8%93%E9%A1%B9%E8%B5%84%E9%87%91%E7%AE%A1%E7%90%86%E5%8A%9E%E6%B3%95%E3%80%8B%E7%9A%84%E9%80%9A%E7%9F%A5%EF%BC%88%EF%BC%89&amp;_token=5e47adc7ac9c1ecd69999b1c978a33d9" onclick="toleaveMessage2();" target="_blank"><i class="iconfont"></i> 政策咨询</a>
                    <span class="collectIcon beCollect" onclick="saveOrCancelCollect()" collectstatus="not"><i class="iconfont beCollectIcon"></i> 收藏</span>
                </div>
                </ul>
	        	<div class="layui-tab-content">
	                <div class="layui-tab-item layui-show">
			            <!-- <div class="eventList-head">
			                <span class="eventListTitle">政策原文</span>
			                <div class="navRightPart" style="top: 2px;line-height: 55px;">
			                    <span class="matching">匹配度：<i>70%</i></span>
			                    <span class="noteIcon" onclick="toleaveMessage();"><i class="iconfont">&#xe6ff;</i> 咨询</span>
			                    <span class="collectIcon beCollect" onclick="saveOrCancelCollect()">
			                    </span>
			                </div>
			            </div> -->
			            <div class="diagramImg zcywCont">
			                <div class="articleNav">
			                    <div class="diagramTitle"><i id="span_title">罗湖区投资促进局局关于印发《深圳市罗湖区产业发展专项资金管理办法》的通知</i>
			                        <p class="informNum" id="policyIssuedNum"></p>
			                    </div>
			                    <div class="articleNavBtm">
			                        <span>发布时间 ： <i><span class="policyPublishDate">2025-06-10</span></i></span>
			                        <span>发布单位 ： <i class="policySource">深圳市罗湖区人民政府</i></span>
			                        <!-- <div class="shareTo">
			                            <span class="shareCheck"><img src="/sqzc/images/shareCheck.png" alt=""></span>
			                            <span class="shareXL"><img src="/sqzc/images/xinlang.png" alt=""></span>
			                            <span class="shareWechat"><img src="/sqzc/images/shareWechat.png" alt=""></span>
			                        </div> -->

<div class="shareTo">
             <!--分享弹窗-->
            <div class="shareCheck bdsharebuttonbox" data-tag="share_1">
               <!--  <a class="bds_more" data-cmd="more"></a> -->
                <a class="bds_tsina" data-cmd="tsina"></a>
                <a class="bds_weixin" data-cmd="weixin"></a>
            </div>
            <!--分享弹窗-->
            
</div>
  
<script type="text/javascript" src="/sqzc/js/share.js"></script>
<script>

/**
 * _content,分享内容
 * _url,分享地址
 * _desc,摘要
 * _pic,图片
 */
function shareInit(_content, _url, _desc, _pic){
	if(_url == null ||this_url.trim() ==""){
		_url = window.location.href;
	}
	if(_desc == null || _desc.trim() == ""){
		_desc = _content;
	}
	window._bd_share_config = {
	        common : {
	            bdText : _content,
	            bdDesc : _desc,
	            bdUrl : _url,
	            bdPic : _pic,
	            bdMiniList:['mshare','tsina','weixin','qzone','tqq','tieba','people']
	        },
	        share : [{
	            "bdSize" : 24
	        }],
	        image : [{
	            viewType : 'list',
	            viewPos : 'top',
	            viewColor : 'black',
	            viewSize : '16',
	            viewList : ['mshare','tsina','weixin','qzone','tqq','tieba','people']
	        }],
	        selectShare : [{
	            "bdselectMiniList" : ['qzone','tqq','kaixin001','bdxc','tqf']
	        }]
	    };
}






       
        
        
</script>    
			                    </div>
			                </div>
			                <!-- <div class="detailedIndex">
			                    <span>
			                        <i class="indexTitle">索&nbsp;引&nbsp;号&nbsp;：</i>
			                        <i class="indexText" id="policyIdentifier"></i>
			                    </span>
			                    <span>
			                        <i class="indexTitle">分&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;类：</i>
			                        <i class="indexText" id="policyClassify"></i>
			                    </span>
			                    <span>
			                        <i class="indexTitle">文&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;号：</i>
			                        <i class="indexText" id="policyIssuedNum2"></i>
			                    </span>
			                    <span>
			                        <i class="indexTitle">成文日期：</i>
			                        <i class="indexText"><span id="policyDate"></span></i>
			                    </span>
			                    <span>
			                        <i class="indexTitle">发布机构：</i>
			                        <i class="indexText"><span id="policypublisher"></span></i>
			                    </span>
			                    <div>
			                        <i class="indexTitle">名 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;称：</i>
			                        <div class="indexText"><span id="policytitle"></span></div>
			                    </div>
			                </div> -->
			                <!-- 正文內容  start-->
			                
			                <!-- <div class="indexInform">
			                    <div class="informTitle">
			                        <h1>广东省人民政府关于印发广东省降低制造业企业成本支持实体经济发展 若干政策措施（修订版）的通知</h1>
			                        <p class="informNum">粤府〔2018〕79号</p>
			                    </div>
			                    <div class="informCont">
			                        <div>各地级以上市人民政府，各县（市、区）人民政府，省政府各部门、各直属机构：</div>
			                        <p>现将《广东省降低制造业企业成本支持实体经济发展的若干政策措施（修订版）》印发给你们，请认真组织实施。实施过程中遇到的问题，请径向省经济和信息化委反映。</p>
			                    </div>
			                    <div class="informFrom">
			                        <p>广东省人民政府</p>
			                        <p>2018年8月31日</p>
			                    </div>
			                </div>  -->
			                <div class="articleContent" id="policyinfo"><p style="text-align: left;"><br></p><p style="text-align: center;">第一章&nbsp;总 &nbsp;则</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　第一条&nbsp;为落实广东省和深圳市关于加快产业发展的战略部署，推动罗湖高质量建设“三力三区”，充分发挥产业发展专项资金的导向和激励作用，加强对专项资金的管理，制定本办法。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　第二条&nbsp;本办法所指的产业发展专项资金（以下简称“专项资金”），是经区政府批准，由区财政预算安排，用于促进全区经济发展的专项资金。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　第三条&nbsp;产业发展主管部门包括：区发展改革局、区科技和工业信息化局、区财政局、区住房和建设局、区商务局、区文化广电旅游体育局、区投资促进局、区金融服务署、区企业服务中心、市市场监督管理局罗湖监管局（以下简称产业主管部门）。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　第四条&nbsp;定期召开区产业发展专项资金联席会议（以下简称“联席会议”），由区长或受区长委托的其他区领导主持召开。原则上每月召开，也可根据上会议题需要适时召开。区长或受区长委托主持会议的区领导、涉及议题的分管区领导，产业主管部门以及区司法局、区审计局、区统计局、区委财经办等与涉及议题相关的部门主要负责人参加。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　第五条&nbsp;区产业发展专项资金联席会议办公室（以下简称“办公室”）设在区企业服务中心，负责组织会议、复核项目、调配和监督产业资金的使用、日常管理等工作。各参会部门负责本单位提请审议项目的受理、初审、会议汇报、公示、拨款、后续监管等。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　第六条&nbsp;产业主管部门要加强分工配合，确保科学、合法、高效使用专项资金。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　产业主管部门及相关部门负责制定本部门专项资金若干措施及配套政策，提供咨询服务，受理和初审扶持项目，对扶持项目进行跟踪监管，负责追回和处置违法违约企业专项资金，并向办公室报备。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　区财政局负责审核监督产业主管部门年度资金预算，编制专项资金年度总额度。配合产业主管部门核实企业经济数据，协助开展专项资金绩效评价工作。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　区纪委监委、区审计局负责依法对专项资金的管理和使用部门进行监督、审计。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　区司法局负责专项资金相关政策文件、重点合同的法律审查，对专项资金疑难复杂事项提供法律意见。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　区民政局、区人力资源局、区卫生健康局、区应急管理局、市生态环境管理局罗湖管理局等部门根据各自职能，配合产业主管部门开展项目审核。&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　第七条&nbsp;专项资金按规定实行预算管理，专款专用。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　产业主管部门根据国家、省、市最新政策精神及上年度资助情况，编制年度专项资金使用方案，确定专项资金扶持的方向、结构、重点，按部门预算编制程序报批。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　区财政局根据产业主管部门专项资金使用方案，结合年度财政收支预算和经济发展目标，提出专项资金预算总规模和各分项资金预算安排意见，一并纳入区年度预算草案，按规定程序报批。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　产业主管部门依据各分项资金若干措施，在批复的分项资金预算额度内进行合理分解。结合上一年度专项资金预算执行情况、绩效目标完成情况和下一年度任务目标，按照预算编制要求制定分项资金年度使用计划。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　产业主管部门可按实际需求和使用情况，在各自预算额度内统筹调配各分管领域的资金额度。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　第八条&nbsp;专项资金的使用坚持公平、公开、公正原则，实行总额控制，当年度经审议通过的项目在年度专项资金使用完毕后，顺延至下一年度拨付。</p><p indenttext="　　" noextractcontent="true" style="text-align: center;">　　第二章&nbsp;&nbsp;扶持对象和方式</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　第九条&nbsp;专项资金主要用于发展辖区经济，对发展质量高、创新成果显著、品牌效应明显、社会效益良好、依法正常经营、对辖区经济发展有突出贡献的企业、社会组织、个体工商户（以下简称“单位”）和个人，可以按照本办法申请专项资金。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　第十条&nbsp;限制和除外情形。存在以下情形的单位及个人，不得获得本专项资金：</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　（一）法人因经营相关原因被深圳信用网列入严重失信主体，或存在严重违法失信行为的；</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　（二）重大财产已被人民法院采取保全措施且可能存在产业拨付资金被法院冻结等情形的；</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　（三）其他经审定不适宜拨付情形的。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　第十一条&nbsp;年度扶持金额累计50万元以上的单位和个人，应与相关产业主管部门签订监管协议或书面承诺，如未按相关要求履约，由产业主管部门追回专项资金。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　产业主管部门应每年核查与本部门签订监管协议或向本部门提交承诺书单位的履约情况。对违反协议或承诺的单位和个人，应要求其按协议书或承诺书的约定退回所获专项资金。</p><p indenttext="　　" noextractcontent="true" style="text-align: center;">　　第三章&nbsp;&nbsp;申请与受理</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　第十二条&nbsp;单位和个人自行登录罗湖区人民政府门户网站或罗湖企业服务平台进行如实申报。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　第十三条&nbsp;申报主体应如实提供申报材料，对申报材料的合法性、真实性、准确性负责，签署电子信用承诺书（申请知识产权相关条款的主体应另外签订知识产权合规性承诺书）。根据《深圳市公共信用信息管理办法》等法律法规，申报信息依法纳入深圳市企业和个人公共信用档案的，由受理部门报送相关信用信息。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　第十四条&nbsp;办公室统一接受单位和个人线上申请，根据申请项目所属类别，将申请材料分送相关产业主管部门审核。产业主管部门在线核查单位和个人申请项目及材料，不符合受理条件的，不予受理；符合受理条件且资料齐备的，按程序进行审核与批准。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　第十五条&nbsp;以项目所属行业为原则，行业的主管部门为相关条款受理部门；新引进项目的跟进部门为条款受理部门。</p><p indenttext="　　" noextractcontent="true" style="text-align: center;">　　第四章&nbsp;&nbsp;审核与批准</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　第十六条&nbsp;专项资金审核与批准程序由产业主管部门初审后，报办公室提请联席会议审议，并向社会公示后核准拨款。产业主管部门应提高审核效率，于5个工作日内完成扶持项目初审，通过“大数据”等方式，减少单位材料申报，加快审核进度。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　第十七条&nbsp;单个项目扶持金额在50万元以下的，可采用简易程序办理；单个项目扶持金额在50万元以上的，提请联席会议审批。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　采用简易程序办理的，由产业主管部门提出初审意见，经办公室复核，提请产业主管部门分管区领导签批同意后，公示核准拨款。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　第十八条&nbsp;产业主管部门受理单位和个人申请后，对单位或项目进行综合审查，确保申报材料的合法性、真实性、准确性。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　（一）产业主管部门对需要第三方审计的项目，按程序选择第三方审计机构对项目进行审计。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　（二）产业主管部门对通过初审的项目，提出资金安排的初步意见，报分管区领导审核确认。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　（三）需签订产业监管协议的项目，应由产业主管部门与单位共同草拟协议，报分管区领导审核确认。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　（四）产业主管部门将分管区领导审核确认的资金安排方案、产业监管协议和相关资料，提交办公室进行复核。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　第十九条&nbsp;经所有流程核查通过的项目面向社会公示，期限不少于3个工作日。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　公示期间有异议的，由受理的产业主管部门在15个工作日内完成异议的调查核实，并于3个工作日内反馈，视调查结果调整专项资金扶持额度，并向办公室报备。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　第二十条&nbsp;单位和个人在同一年度内就同类项目，原则上只能申请一次扶持。同一项目当年度设定扶持规模上限的，当年拨付金额不超过该上限，具体分配规则由产业主管部门明确；拨付给单位和个人的产业资金，通过协议或双方协商明确上限的，原则上不超过协议或协商确定的上限。</p><p indenttext="　　" noextractcontent="true" style="text-align: center;">　　第五章&nbsp;&nbsp;监督与检查</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　第二十一条&nbsp;产业主管部门每年对本部门受理的上一年度扶持项目实施情况和资金使用情况进行跟踪管理和绩效评价。监管本部门签订的产业监管协议履行情况，评估受扶持项目（企业、行业）的发展情况和相应扶持资金的使用绩效，形成专题报告报办公室。办公室汇总产业主管部门的评价报告，并形成专项资金使用情况和绩效评价报告。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　第二十二条&nbsp;工作人员违反本办法，存在履职不尽责，与项目申报单位串通、弄虚作假、以权谋私等情形，按照有关规定进行处理；构成犯罪的，依法追究刑事责任。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　第二十三条&nbsp;对于单位或个人在申报、执行项目过程中出现的虚报、冒领、截留、挪用、挤占专项资金等行为，拒绝配合专项资金监督检查，违反书面承诺，由相关部门责令改正，按照《财政违法行为处罚处分条例》对责任人进行处理，依法追回专项资金；构成犯罪的，依法追究刑事责任。</p><p indenttext="　　" noextractcontent="true" style="text-align: center;">　　第六章&nbsp;&nbsp;附 &nbsp;则</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　第二十四条&nbsp;本办法规定的“以上”，包含本数；规定的“以下”，不包含本数。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　第二十五条&nbsp;各配套政策暂未涵盖，但对辖区产业发展有重要影响的事项，经联席会议审议通过后，可使用本专项资金。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　第二十六条&nbsp;单位及个人申请扶持需具备的具体条件以及申报项目适用的扶持方式和额度，以各若干措施为准。</p><p indenttext="　　" noextractcontent="true" style="text-align: left;">　　第二十七条&nbsp;本办法自印发之日起施行。</p><p><br></p></div>
			                <!-- 正文內容  end-->
			                <div class="relatedAccessory fileInfos fileUrlList">
			                    <h4>附件</h4>
			                </div>
			            </div>
			
			         </div>
	                <div class="layui-tab-item">
	                	<div class="diagramImg">
			                <div class="articleNav">
			                    <p class="diagramTitle"><i id="span_title2"></i></p>
			                    <div class="articleNavBtm">
			                        <span>发布时间 ： <i><span id="policyanalysisupdateDate"></span></i></span>
			                        <span>发布单位 ： <i id="policyanalysissource"></i></span>

<div class="shareTo">
             <!--分享弹窗-->
            <div class="shareCheck bdsharebuttonbox" data-tag="share_1">
               <!--  <a class="bds_more" data-cmd="more"></a> -->
                <a class="bds_tsina" data-cmd="tsina"></a>
                <a class="bds_weixin" data-cmd="weixin"></a>
            </div>
            <!--分享弹窗-->
            
</div>
  
<script type="text/javascript" src="/sqzc/js/share.js"></script>
<script>

/**
 * _content,分享内容
 * _url,分享地址
 * _desc,摘要
 * _pic,图片
 */
function shareInit(_content, _url, _desc, _pic){
	if(_url == null ||this_url.trim() ==""){
		_url = window.location.href;
	}
	if(_desc == null || _desc.trim() == ""){
		_desc = _content;
	}
	window._bd_share_config = {
	        common : {
	            bdText : _content,
	            bdDesc : _desc,
	            bdUrl : _url,
	            bdPic : _pic,
	            bdMiniList:['mshare','tsina','weixin','qzone','tqq','tieba','people']
	        },
	        share : [{
	            "bdSize" : 24
	        }],
	        image : [{
	            viewType : 'list',
	            viewPos : 'top',
	            viewColor : 'black',
	            viewSize : '16',
	            viewList : ['mshare','tsina','weixin','qzone','tqq','tieba','people']
	        }],
	        selectShare : [{
	            "bdselectMiniList" : ['qzone','tqq','kaixin001','bdxc','tqf']
	        }]
	    };
}






       
        
        
</script>    
			                    </div>
			                </div>
							
							<div id="policyanalysisinfo">
								<!-- <img src="/sqzc/images/zcjdxq.jpg" alt=""> -->
							</div>
			                
			            </div>
	                </div>
                </div>
            </div>
       	</div>
        	
        <div class="diagramRight">
        	<div style="display: none;">
                <div class="eventList-head">
                    <span class="eventListTitle">相关政策</span>
                    <span class="eventListMore" onclick="morePolicy();">
                    	<a href="JavaScript:;" target="_blank">
                    		<i class="moreText">更多</i><i class="iconfont icon-jiantou-copy"></i>
                    	</a>
                    </span>
                </div>
                <ul class="diagramRightList" id="relatedPolicy">
                <li class="no-data">暂无</li></ul>
            </div>
     	   	<div style="display: none;">
                <div class="eventList-head">
                    <span class="eventListTitle">报项目</span>
                    <span class="eventListMore" onclick="moreProject();">
                    	<a href="JavaScript:;" target="_blank">
                    		<i class="moreText">更多</i><i class="iconfont icon-jiantou-copy"></i> 
                    	</a>
                    </span>
                </div>
                <ul class="diagramRightList" id="relatedProject">
                <li class="no-data">暂无</li></ul>
            </div>
            <div style="display: none;">
                <div class="eventList-head">
                    <span class="eventListTitle">媒体解读</span>
                    <span class="eventListMore" onclick="morePolicyAnalysis();">
	                    <!-- <a href="JavaScript:;" target="_blank">
	                    	<i class="moreText">更多</i><i class="iconfont icon-jiantou-copy"></i> 
						</a> -->
					</span>
                </div>
                <ul class="diagramRightList" id="relatedPolicyAnalysis">
                <li class="no-data">暂无</li></ul>
            </div>
            <!-- <div class="askAndAnswers border" style="display: none;">
                <div class="eventList-head">
                    <span class="eventListTitle">相关咨询</span>
                    <span class="eventListMore" onclick="toAskAnswers()">
	                    <a href="JavaScript:;" target="_blank">
	                    	<i class="moreText">更多</i><i class="iconfont icon-jiantou-copy"></i> 
	                    </a>
                    </span>
                </div>
                <ul class="diagramRightList relatedConsult" id="relatedConsult"></ul>
            </div> -->
            <div class="questionnaireDiv border none">
                <div class="eventList-head">
                    <span class="eventListTitle">调查问卷</span>
                    <span class="eventListMore">
                    	<a target="_blank" href="/sqzc/m/dept/questionnaire/toQuestionnaireList">
		                    <i class="moreText">更多</i>
                    	</a>
                    </span>
                </div>
                <div id="questionnaire">
                	<!-- <form class="layui-form diaochati"  action="">
                	
                	</form>
                	<div>
                		<span class="showAllQuestions" onclick="showAllQuestions()">展开全部</span>
                		<span class="showOneQuestion none" onclick="showOneQuestion()">收起</span>
                		<span class="submitQuestionnaire none" onclick="submitQuestionnaire()">提交</span>
                		<span class="questionPage">
	                		<span class="lastQuestion none">上一题</span>
	                		<span class="nextQuestion">下一题</span>
                		</span>
                	</div> -->
                </div>
            </div>
        </div>
    </div>
    <!--政策原文-->
</div>
<!-- 正文end -->
<!-- 手机端_筛选当前页信息 -->
<div class="dibutangchu">
    
</div>
<!-- 筛选当前页信息 -->
<!--  手机端_主体内容-->

<!-- 正文end -->

<!-- <link rel="stylesheet" href="/sqzc/css/gvmFoot.css" media="all"> -->
<link rel="stylesheet" href="/sqzc/css/foot.css" media="all">
<script type="text/javascript" src="/sqzc/js/other/foot.js"> </script>

<!--底部脚本-->
<footer class="gvmFoot gvmFooter">
    <!--底部导航条-->
    <div class="Links">
        <div class="linkCon">
            <div class="button"><a href="http://www.gov.cn/">中国政府网</a></div>
            <span class="shu"></span>

            <div class="cusSelect" id="gjbw">
                <div class="cusTitle">国务院部门网站</div>
                <ul class="cusOpSet" style="bottom: 40px; left: -15px; right: unset; display: none;">
                    <li class="cusOption"><a target="_blank" href="http://www.fmprc.gov.cn/web/">外交部</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.mod.gov.cn/">国防部</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.ndrc.gov.cn/">国家发展和改革委员会</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.moe.gov.cn/">教育部</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.most.gov.cn/">科学技术部</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.miit.gov.cn/">工业和信息化部</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.seac.gov.cn/">国家民族事务委员会</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.mps.gov.cn/">公安部</a></li>
                    <li class="cusOption"><a target="_blank" href="javascript:;">国家安全部</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.mca.gov.cn/">民政部</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.moj.gov.cn/">司法部</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.mof.gov.cn/index.htm">财政部</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.mohrss.gov.cn/">人力资源和社会保障部</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.mnr.gov.cn/">自然资源部</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.mee.gov.cn/">生态环境部</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.mohurd.gov.cn/">住房和城乡建设部</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.mot.gov.cn/">交通运输部</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.mwr.gov.cn/">水利部</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.moa.gov.cn/">农业农村部</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.mofcom.gov.cn/">商务部</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.mct.gov.cn/">文化和旅游部</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.nhfpc.gov.cn/">国家卫生健康委员会</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.mva.gov.cn/">退役军人事务部</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.chinasafety.gov.cn/index.shtml">应急管理部</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.pbc.gov.cn/">人民银行</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.audit.gov.cn/">审计署</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.china-language.gov.cn/">国家语言文字工作委员会</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.safea.gov.cn/">国家外国专家局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.cnsa.gov.cn/">国家航天局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.caea.gov.cn/">国家原子能机构</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.soa.gov.cn/">国家海洋局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://nnsa.mep.gov.cn/">国家核安全局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.sasac.gov.cn/">国务院国有资产监督管理委员会</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.customs.gov.cn/">海关总署</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.chinatax.gov.cn/">国家税务总局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://samr.saic.gov.cn/">国家市场监督管理总局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.sapprft.gov.cn/">国家广播电视总局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.sport.gov.cn/">国家体育总局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.stats.gov.cn/">国家统计局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.cidca.gov.cn/">国家国际发展合作署</a></li>
                    <li class="cusOption"><a target="_blank" href="javascript:;">国家医疗保障局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.counsellor.gov.cn/">国务院参事室</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.ggj.gov.cn/">国家机关事务管理局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.cnca.gov.cn/">国家认证认可监督管理委员会</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.sac.gov.cn/">国家标准化管理委员会</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.ncac.gov.cn/">国家新闻出版署（国家版权局）</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.sara.gov.cn/">国家宗教事务局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.hmo.gov.cn/">国务院港澳事务办公室</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.gov.cn/guoqing/2018-06/22/content_5300522.htm">国务院研究室</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.gqb.gov.cn/">国务院侨务办公室</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.gwytb.gov.cn/">国务院台湾事务办公室</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.cac.gov.cn/">国家互联网信息办公室</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.scio.gov.cn/index.htm">国务院新闻办公室</a></li>
                    <li class="cusOption"><a target="_blank" href="http://203.192.6.89/xhs/">新华通讯社</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.cas.ac.cn/">中国科学院</a></li>
                    <li class="cusOption"><a target="_blank" href="http://cass.cssn.cn/">中国社会科学院</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.cae.cn/">中国工程院</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.drc.gov.cn/">国务院发展研究中心</a></li>
                    <li class="cusOption"><a target="_blank" href="javascript:;">中央广播电视总台</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.cma.gov.cn/">中国气象局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.cbrc.gov.cn/">中国银行保险监督管理委员会</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.csrc.gov.cn/pub/newsite/">中国证券监督管理委员会</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.nsa.gov.cn/">国家行政学院</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.gjxfj.gov.cn/">国家信访局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.chinagrain.gov.cn/index.html">国家粮食和物资储备局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.nea.gov.cn/">国家能源局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.sastind.gov.cn/">国家国防科技工业局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.tobacco.gov.cn/html/">国家烟草专卖局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.mps.gov.cn/n2254996/">国家移民管理局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.forestry.gov.cn/">国家林业和草原局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.nra.gov.cn/">国家铁路局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.caac.gov.cn/index.html">中国民用航空局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.spb.gov.cn/">国家邮政局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.sach.gov.cn/">国家文物局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.satcm.gov.cn/">国家中医药管理局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.chinacoal-safety.gov.cn/">国家煤矿安全监察局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.safe.gov.cn/">国家外汇管理局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.nmpa.gov.cn/WS04/CL2042/">国家药品监督管理局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.cnipa.gov.cn">国家知识产权局</a></li>
                    <li class="cusOption"><a target="_blank" href="javascript:;">出入境管理局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.forestry.gov.cn/">国家公园管理局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.scs.gov.cn/">国家公务员局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.saac.gov.cn/">国家档案局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.gjbmj.gov.cn">国家保密局</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.oscca.gov.cn/">国家密码管理局</a></li>
                </ul>
            </div>
            <span class="shu"></span>

            <div class="cusSelect" id="sqszf">
                <div class="cusTitle">各省区市网站</div>
                <ul class="cusOpSet" style="bottom: 40px; left: -15px; right: unset; display: none;">
                    <li class="cusOption"><a target="_blank" href="http://www.beijing.gov.cn/">北京</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.tj.gov.cn/">天津</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.hebei.gov.cn/">河北</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.shanxi.gov.cn">山西</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.nmg.gov.cn/">内蒙古</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.ln.gov.cn">辽宁</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.jl.gov.cn">吉林</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.hlj.gov.cn">黑龙江</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.shanghai.gov.cn/">上海</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.jiangsu.gov.cn/">江苏</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.zhejiang.gov.cn">浙江</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.ah.gov.cn/">安徽</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.fujian.gov.cn">福建</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.jiangxi.gov.cn">江西</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.shandong.gov.cn/">山东</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.henan.gov.cn/">河南</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.hubei.gov.cn/">湖北</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.hunan.gov.cn/">湖南</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.gd.gov.cn/">广东</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.gxzf.gov.cn/">广西</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.hainan.gov.cn/">海南</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.cq.gov.cn/">重庆</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.sc.gov.cn">四川</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.guizhou.gov.cn">贵州</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.yn.gov.cn/">云南</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.xizang.gov.cn/">西藏</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.shaanxi.gov.cn/">陕西</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.gansu.gov.cn/">甘肃</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.nx.gov.cn/">宁夏</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.qh.gov.cn/">青海</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.xinjiang.gov.cn/">新疆</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.gov.hk/">香港</a></li>
                    <li class="cusOption"><a target="_blank" href="http://portal.gov.mo/web/guest/welcomepage">澳门</a></li>
                    <!-- <option disabled="">台湾</option> -->
                    <li class="cusOption"><a>台湾</a></li>
                </ul>
            </div>
            <span class="shu"></span>

            <div class="cusSelect" id="zgajg">
                <div class="cusTitle">驻港澳机构网站</div>
                <ul class="cusOpSet" style="bottom: 40px; left: -15px; right: unset; display: none;">
                    <li class="cusOption"><a target="_blank" href="http://www.locpg.gov.cn/">中央政府驻港联络办</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.zlb.gov.cn/">中央政府驻澳门联络办</a></li>
                </ul>
            </div>

            <span class="shu"></span>
            <div class="cusSelect" id="szf">
                <div class="cusTitle">省政府机构网站</div>
                <ul class="cusOpSet">
                    <li class="cusOption"><a href="http://www.gd.gov.cn/">省政府办公厅</a></li>
                    <li class="cusOption"><a href="http://drc.gd.gov.cn">省发展改革委</a></li>
                    <li class="cusOption"><a href="http://edu.gd.gov.cn">省教育厅</a></li>
                    <li class="cusOption"><a href="http://gdstc.gd.gov.cn">省科技厅</a></li>
                    <li class="cusOption"><a href="http://gdii.gd.gov.cn">省工业和信息化厅</a></li>
                    <li class="cusOption"><a href="http://mzzjw.gd.gov.cn">省民族宗教委</a></li>
                    <li class="cusOption"><a href="http://gdga.gd.gov.cn/">省公安厅</a></li>
                    <li class="cusOption"><a href="http://smzt.gd.gov.cn">省民政厅</a></li>
                    <li class="cusOption"><a href="http://sft.gd.gov.cn">省司法厅</a></li>
                    <li class="cusOption"><a href="http://czt.gd.gov.cn/">省财政厅</a></li>
                    <li class="cusOption"><a href="http://www.gdhrss.gov.cn/">省人力资源社会保障厅</a></li>
                    <li class="cusOption"><a href="http://nr.gd.gov.cn">省自然资源厅</a></li>
                    <li class="cusOption"><a href="http://gdee.gd.gov.cn/">省生态环境厅</a></li>
                    <li class="cusOption"><a href="http://zfcxjst.gd.gov.cn/">省住房城乡建设厅</a></li>
                    <li class="cusOption"><a href="http://td.gd.gov.cn">省交通运输厅</a></li>
                    <li class="cusOption"><a href="http://slt.gd.gov.cn">省水利厅</a></li>
                    <li class="cusOption"><a href="http://dara.gd.gov.cn">省农业农村厅</a></li>
                    <li class="cusOption"><a href="http://com.gd.gov.cn">省商务厅</a></li>
                    <li class="cusOption"><a href="http://www.gdwht.gov.cn/">省文化和旅游厅</a></li>
                    <li class="cusOption"><a href="http://wsjkw.gd.gov.cn">省卫生健康委</a></li>
                    <li class="cusOption"><a href="http://dva.gd.gov.cn/">省退役军人事务厅</a></li>
                    <li class="cusOption"><a href="http://yjgl.gd.gov.cn">省应急管理厅</a></li>
                    <li class="cusOption"><a href="http://gdaudit.gd.gov.cn/">省审计厅</a></li>
                    <li class="cusOption"><a href="http://gzw.gd.gov.cn">省国资委</a></li>
                    <li class="cusOption"><a href="http://amr.gd.gov.cn">省市场监管局</a></li>
                    <li class="cusOption"><a href="http://gbdsj.gd.gov.cn">省广电局</a></li>
                    <li class="cusOption"><a href="http://www.tyj.gd.gov.cn/">省体育局</a></li>
                    <li class="cusOption"><a href="http://stats.gd.gov.cn">省统计局</a></li>
                    <li class="cusOption"><a href="http://hsa.gd.gov.cn">省医保局</a></li>
                    <li class="cusOption"><a href="http://hmo.gd.gov.cn/">省港澳办</a></li>
                    <li class="cusOption"><a href="http://gdjr.gd.gov.cn">省地方金融监管局</a></li>
                    <li class="cusOption"><a href="http://www.gdcss.gd.gov.cn/">省参事室 </a></li>
                    <li class="cusOption"><a href="http://gdwsxf.gd.gov.cn/">省信访局</a></li>
                    <li class="cusOption"><a href="http://zfsg.gd.gov.cn/">省政务服务数据管理局</a></li>
                    <li class="cusOption"><a href="http://gdgrain.gd.gov.cn">省粮食和储备局</a></li>
                    <li class="cusOption"><a href="http://gdnpo.gd.gov.cn">省社会组织管理局</a></li>
                    <li class="cusOption"><a href="http://gdjyj.gd.gov.cn">省监狱局</a></li>
                    <li class="cusOption"><a href="http://gdjdj.gd.gov.cn">省戒毒局</a></li>
                    <li class="cusOption"><a href="http://lyj.gd.gov.cn">省林业局</a></li>
                    <li class="cusOption"><a href="http://szyyj.gd.gov.cn">省中医药局</a></li>
                    <li class="cusOption"><a href="http://mpa.gd.gov.cn">省药监局</a></li>
                    <li class="cusOption"><a href="http://gdio.southcn.com/">省新闻办</a></li>
                    <li class="cusOption"><a href="http://www.bmj.gd.gov.cn/">省保密局</a></li>
                    <li class="cusOption"><a href="http://www.gdas.gd.cn/">省科学院</a></li>
                    <li class="cusOption"><a href="http://www.gdass.gov.cn/">省社科院</a></li>
                    <li class="cusOption"><a href="http://www.gdaas.cn/">省农科院</a></li>
                    <li class="cusOption"><a href="http://gdyjzx.gd.gov.cn">省发展研究中心</a></li>        
                    <li class="cusOption"><a href="http://dfz.gd.gov.cn/">省地方志办</a></li>
                     <li class="cusOption"><a href="http://djj.gd.gov.cn">省代建局</a></li>                       
                    <li class="cusOption"><a href="http://dzj.gd.gov.cn">省地质局</a></li>
                    <li class="cusOption"><a href="http://gdhgy.gd.gov.cn">省核工业地质局</a></li>
                    <li class="cusOption"><a href="http://coop.gd.gov.cn">省供销社</a></li>
                    <li class="cusOption"><a href="http://eea.gd.gov.cn">省考试院</a></li>
                    <li class="cusOption"><a href="http://gdae.gdedu.gov.cn/">省教育研究院</a></li>
                    <li class="cusOption"><a href="http://www.fenxi.com.cn/client/home/<USER>">省测试分析研究所</a></li>
                    <li class="cusOption"><a href="http://www.gdppnet.com//">省生产力促进中心</a></li>
                    <li class="cusOption"><a href="http://www.gdsc.cn/">省科学中心</a></li>
                    <li class="cusOption"><a href="http://www.gdsi.gov.cn/">省社保基金管理局</a></li>
                    <li class="cusOption"><a href="http://www.gdadri.com/">省建筑设计院</a></li>
                    <li class="cusOption"><a href="http://swj.gd.gov.cn">省水文局</a></li>
                    <li class="cusOption"><a href="http://bjly.gd.gov.cn">省北江流域管理局</a></li>
                    <li class="cusOption"><a href="http://www.gdghospital.org.cn/">省人民医院</a></li>
                    <li class="cusOption"><a href="http://cdcp.gd.gov.cn">省疾控中心</a></li>
                    <li class="cusOption"><a href="http://www.tyj.gd.gov.cn/4294295/4308053.html">省体育二沙运动训练中心</a></li>
                    <li class="cusOption"><a href="http://www.gqi.org.cn/">省质检院</a></li>
                    <li class="cusOption"><a href="http://www.gdysdz.com/">省有色金属地质局</a></li>
                    <li class="cusOption"><a href="http://www.szdzj.cn/">深圳市地质局</a></li>
                </ul>
            </div>

            <span class="shu"></span>
            <div class="cusSelect" id="sxzf">
                <div class="cusTitle">地级以上市网站</div>
                <ul class="cusOpSet" style="bottom: 40px; left: -15px; right: unset; display: none;">
                    <li class="cusOption"><a target="_blank" href="http://www.gz.gov.cn/">广州</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.sz.gov.cn/">深圳</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.zhuhai.gov.cn/">珠海</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.shantou.gov.cn/">汕头</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.foshan.gov.cn/">佛山</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.sg.gov.cn/ ">韶关</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.heyuan.gov.cn/">河源</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.meizhou.gov.cn/">梅州</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.huizhou.gov.cn/">惠州</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.shanwei.gov.cn/">汕尾</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.dongguan.gov.cn/">东莞</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.zs.gov.cn/">中山</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.jiangmen.gov.cn/">江门</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.yangjiang.gov.cn/">阳江</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.zhanjiang.gov.cn/">湛江</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.maoming.gov.cn/">茂名</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.zhaoqing.gov.cn/">肇庆</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.gdqy.gov.cn/">清远</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.chaozhou.gov.cn/">潮州</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.jieyang.gov.cn/">揭阳</a></li>
                    <li class="cusOption"><a target="_blank" href="http://www.yunfu.gov.cn/">云浮</a></li>
                </ul>
            </div>
        </div>
    </div>
    <!--底部导航条-->
    
    <!--底部主体-->
    <div class="info">
        <div class="info-itm zficon">
            <div class="jiucuo">
                <script id="_jiucuo_" sitecode="4400000131" src="/sqzc/js/other/jiucuo.js"></script>
                <span id="_span_jiucuo">
                    <img onclick="Link('4400000131')" style="margin:0;border:0;cursor: pointer;" src="/sqzc/images/jiucuo.png?v=4400000131">
                </span>
            </div>
            <div class="ideConac">
                <span id="_ideConac">
                	<a href="http://bszs.conac.cn/sitename?method=show&amp;id=058751C9AA454F26E053012819ACD379"><img src="/sqzc/images/red2.png"></a>
                </span>
            </div>
        </div>
        <div class="info-itm  wzinfo">
            <div class="wzinfo-itm active">网站信息</div>
            <div class="wzinfo-itm">
                <a href="http://www.gd.gov.cn/yw/content/post_100915.html" target="_blank">关于本网</a>
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <a href="http://www.gd.gov.cn/yw/content/post_2169593.html" target="_blank">网站声明</a>
            </div>
            <div class="wzinfo-itm">
                <a href="http://www.gd.gov.cn/yw/content/post_104984.html" target="_blank">网站地图</a>
            </div>
        </div>
        <div class="info-itm  wzinfo">
            <div class="wzinfo-itm active">联系我们</div>
            <div class="wzinfo-itm">
                <p>020-83135078</p>
                <p style="font-size: 12px; color: #777;">仅受理网站建设维护相关事宜</p>
            </div>
            <div class="wzinfo-itm"><EMAIL></div>
        </div>
		<div class="info-itm  wzinfo xmtjz">
            <div class="wzinfo-itm active" style="cursor:auto;">新媒体矩阵</div>
            <div class="wzinfo-itm code1">
                <img src="/sqzc/images/qr.png">网站官方微信公众号
            </div>
            <div class="wzinfo-itm">
                <div class="code2" style="display:inline-block;width: 47%;"> 
                    <img src="/sqzc/images/qr.png">粤省事小程序
                 </div>
                <div class="code3" style="display:inline-block;width: 45%; float:right;">
                    <img src="/sqzc/images/qr.png">粤商通APP
                </div>
            </div>
        </div>
        <div class="QrCode qr_1 hide">
            <img src="/sqzc/images/qrCode_3.png">
        </div>
        <div class="QrCode qr_2 hide">
            <img src="/sqzc/images/qrCode_2.png">
        </div>
        <div class="QrCode qr_3 hide">
            <img src="/sqzc/images/qrCode_4.jpg">
        </div>

    </div>
    <!--底部主体-->

    <!--页尾-->
    <div class="copyright">
        <div class="footCon">
            <span>主办：&nbsp;&nbsp;&nbsp;广东省人民政府办公厅&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
            <span>承办：&nbsp;&nbsp;&nbsp;南方新闻网&nbsp;&nbsp;&nbsp;数字广东网络建设有限公司</span>
            <span class="right"><p>版权所有：广东省人民政府门户网站</p><a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=44010402001160"><p class="gab"><img src="/sqzc/images/icon_ga.png" style="float:left; width:20px; height:20px; margin-top: 14px;">粤公网安备 44010402001160号</p></a><p>粤ICP备05070829号-1&nbsp;&nbsp;&nbsp;网站标识码4400000131</p></span>
        </div>
    </div>
    <!--页尾-->
</footer>
<div class="footer_app">
	<div class="layui-container" style="padding: 0;">  
	  <div class="layui-row layui-col-space10 fujian">
		<div class="layui-col-sm3 layui-col-xs3">	    	
			<div class="fujianList" onclick="goConsult();">
		    	<i class="iconfont icon-file-text-o"></i><p>企业诉求</p>
	    	</div>
	    </div>
	    <div class="layui-col-sm3 layui-col-xs3">
		    <div class="fujianList" onclick="service();">
		    	<i class="iconfont icon-zixun1"></i><p>客 服</p>
	    	</div>
	    </div>
	    <div class="layui-col-sm3 layui-col-xs3">
		    <div class="fujianList" onclick="help();">
		    	<i class="iconfont icon-bangzhu5"></i><p>帮 助</p>
	    	</div>
	    </div>
	    <div class="layui-col-sm3 layui-col-xs3">
		    <div class="fujianList" onclick="suggest();">
		    	<i class="iconfont icon-tousujianyi"></i><p>网站纠错</p>
	    	</div>
	    </div>
	    
	  </div>
    </div>
	<div class="banquan">版权：广东省人民政府门户网站</div>
</div>


<!--底部脚本-->

<script>
$(function () {
//  底部最新脚本-----------------新增
  $(".code1").on("click", function () {
      $(".qr_2").removeClass("hide").addClass("hide");
      $(".qr_3").removeClass("hide").addClass("hide");
      $(".qr_1").toggleClass("hide");

  });
  $(".code2").on("click", function () {
      $(".qr_1").removeClass("hide").addClass("hide");
      $(".qr_3").removeClass("hide").addClass("hide");
      $(".qr_2").toggleClass("hide");
  });
  
  $(".code3").on("click", function () {
      $(".qr_1").removeClass("hide").addClass("hide");
      $(".qr_2").removeClass("hide").addClass("hide");
      $(".qr_3").toggleClass("hide");
  });
  //            底部最新脚本-----------------新增

})
//企业诉求
function goConsult() {
	  window.open('https://ysx.gdzwfw.gov.cn/dashboard');
}
//智能客服
function service() {
	  //window.open('http://znkf.digitalgd.com.cn/gdbs/customer.html?CId=01');
	  window.open('https://rec.southcn.com/');
}
//交流互动
function communicate(){
	  window.open('/sqzc/m/cms/consult/toLeaveMessage?title=');
}
//帮助
function help(){
	if(browserRedirect()){
		window.location.href = '/sqzc/m/cms/help';
	}else{
		window.open('/sqzc/m/cms/help');
	}
	
}
//投诉建议
function suggest(){
	  /* if(browserRedirect()){
	  		window.location.href = '/sqzc/m/cms/suggest';
		}else{
			window.open('/sqzc/m/cms/suggest');;
		} */
	  if(browserRedirect()){
	  		window.location.href = '/sqzc/m/dept/questionnaire/toQuestionnaireInfo?id=238c5300f7884710a7615e5296ed2e16';
		}else{
			window.open('/sqzc/m/dept/questionnaire/toQuestionnaireInfo?id=238c5300f7884710a7615e5296ed2e16');;
		}
}  
</script><!-- 腳本國際化 -->
<script type="text/javascript">
	var _i18n_validate_required = "<span class='hidden'></span>";//"请填写此信息！";
	var _i18n_validate_remote = "请修正该字段！";
	var _i18n_validate_email = "请输入正确格式的电子邮件！";
	var _i18n_validate_url = "请输入合法的网址！";
	var _i18n_validate_date = "请输入合法的日期！";
	var _i18n_validate_dateISO = "请输入正确的日期 (ISO)！";
	var _i18n_validate_number = "请输入正确的数字！";
	var _i18n_validate_digits = "只能输入整数！";
	var _i18n_validate_creditcard = "请输入合法的信用卡号！";
	var _i18n_validate_equalTo = "请再次输入相同的值！";
	var _i18n_validate_accept = "请输入合法后缀名的信息！";
	var _i18n_validate_maxlength = "请输入最多 {0}位的信息！";
	var _i18n_validate_minlength = "请输入不少于 {0}位的信息！";
	var _i18n_validate_rangelength = "请输入 {0} 至{1}位的信息！";
	var _i18n_validate_range = "请输入介于 {0}和 {1}之间的值！";
	var _i18n_validate_max = "请输入最大为 {0}的值！";
	var _i18n_validate_min = "请输入最小为 {0}的值！	";
	var _i18n_validate_telephone = "请输入正确的手机号码！	";
	var _i18n_validate_idCard = "请输入正确的身份证号码！	";
	var _i18n_bootbox_ok = "确定";
	var _i18n_bootbox_cancel = "取消";
	var _i18n_bootbox_confirm = "确认";
	
	var _i18n_iscroll_release = "释放立刻刷新...";
	var _i18n_iscroll_pulldown = "下拉刷新...";
	var _i18n_iscroll_pullup = "上拉加载更多...";
	var _i18n_iscroll_loading = "加载中...";
	var _i18n_iscroll_nomore = "沒有更多！";
	var _i18n_iscroll_loaderror = "加载出错！";
	
	var _i18n_app_set_collect_name="";
	var _i18n_app_common_collect_cancel="取消收藏";
	var _i18n_app_home_menu_collect="收藏 ";
	var admin_common_success="成功";
	var admin_common_fail="失败";
	var admin_common_cancel="取消";
	
	var _i18n_dialog_type_default = "消息";
	var _i18n_dialog_type_info = "消息";
	var _i18n_dialog_type_primary = "消息";
	var _i18n_dialog_type_success = "成功";
	var _i18n_dialog_type_warning = "警告";
	var _i18n_dialog_type_danger = "危险";
	var _i18n_dialog_button_ok = "确定";
	var _i18n_dialog_button_cancel = "取消	";
	var _i18n_dialog_button_confirmation = "确定";
</script>
<script src="/sqzc/js/main.js?v=20211025&amp;t=1.152" type="text/javascript" charset="utf-8"></script><script type="text/javascript" src="/sqzc/js/layui/layui.js"></script>
<script type="text/javascript" src="/sqzc/js/other/html5shiv.js"> </script>
<script type="text/javascript" src="/sqzc/js/other/respond.min.js"> </script>
<script type="text/javascript" src="/sqzc/js/common_layui.js"> </script>


<script type="text/javascript" src="/sqzc/js/common.js"> </script>
<script type="text/javascript">


var cxt = '/sqzc';
var _apiKey = '';
var _owm = 'false';
if(browserRedirect()){
	var popup = new auiPopup();
}
function hidePopup(){
    popup.hide(document.getElementById("top-left"))
}
function surePopup(){
    popup.hide(document.getElementById("top-left"))
}


//定義js
var app_js = {
	//"bootstrap":"/sqzc/jsFile/jquery/bootstrap/bootstrap.js?v1",
	"footer":"/sqzc/js/footer.js",
	"common":"/sqzc/js/common.js?v=1.16",
	"jquery_form":"/sqzc/js/jquery/form/jquery.form.js",
	"jquery_validate":"/sqzc/js/jquery/validate/jquery.validate.min.js",
	
	"infinitescroll":"/sqzc/jsFile/jquery/infinite-scroll/jquery.infinitescroll.min.js",
	"toastr":"/sqzc/jsFile/jquery/toastr/toastr.min.js",
	"iscroll":"/sqzc/ui/exappui/plugins/iScroll/iscroll.js",
	"jquery_bootbox":"/sqzc/jsFile/jquery/bootbox/bootbox.min.js",
	"jquery_mobiscroll":"/sqzc/jsFile/jquery/mobiscroll/scripts/mobiscroll-2.13.2.full.min.js",
	"jquery_clipboard":"/sqzc/jsFile/jquery/clipboard/clipboard.min.js"
};
	
//定義css
var app_css = {
	"font-awesome":"/sqzc/css/bootstrap/font-awesome.min.css",
	"glyphicons-halflings-regular":"/sqzc/css/bootstrap/glyphicons-halflings-regular.css",
	"toastr":"/sqzc/js/jquery/toastr/toastr.css",
	"jquery_mobiscroll":"/sqzc/jsFile/jquery/mobiscroll/content/mobiscroll-2.13.2.full.min.css"
};
</script>
<script type="text/javascript">
/* head.js(app_js.common);
head.js(app_css["font-awesome"],app_css["glyphicons-halflings-regular"]); */
//加載完成事件
head.ready(function(){
});


function saveLog(msg){
	var a = new Error(msg);
	a.type = 'info';
	var data = {};
	data.msg= a.message;
	data.log_type = a.type;
	data.error =  a.stack;
	common.ajaxFunc("/sqzc/app/common/jslog/savelog", data, "json", function(a) {
	        });
}

/**
     * @param {String}  errorMessage   错误信息
     * @param {String}  scriptURI      出错的文件
     * @param {Long}    lineNumber     出错代码的行号
     * @param {Long}    columnNumber   出错代码的列号
     * @param {Object}  errorObj       错误的详细信息，Anything
     */
window.onerror = function(msg,url,line,col,error){
    //没有URL不上报！上报也不知道错误
    try{
    	try{console.dir(arguments);}catch(e){}
	    if (msg != "Script error." && !url){
	        return true;
	    }
	    //采用异步的方式
	    //我遇到过在window.onunload进行ajax的堵塞上报
	    //由于客户端强制关闭webview导致这次堵塞上报有Network Error
	    //我猜测这里window.onerror的执行流在关闭前是必然执行的
	    //而离开文章之后的上报对于业务来说是可丢失的
	    //所以我把这里的执行流放到异步事件去执行
	    //脚本的异常数降低了10倍
	    setTimeout(function(){
	        var data = {};
	        //不一定所有浏览器都支持col参数
	        col = col || (window.event && window.event.errorCharacter) || 0;
	        data.msg = msg;
	        data.url = location.href;
	        data.line = line;
	        data.col = col;
	       	data.log_type = 'error';
	        if(error&&error.type=="info")
	        data.log_type = 'info';
	        if (!!error && !!error.stack){
	            //如果浏览器有堆栈信息
	            //直接使用
	            data.error = error.stack.toString();
	        }else if (!!arguments.callee){
	            //尝试通过callee拿堆栈信息
	            var ext = [];
	            var f = arguments.callee.caller, c = 3;
	            //这里只拿三层堆栈信息
	            while (f && (--c>0)) {
	               ext.push(f.toString());
	               if (f  === f.caller) {
	                    break;//如果有环
	               }
	               f = f.caller;
	            }
	            ext = ext.join(",");
	            data.error = ext;
	        }
	        //把data上报到后台！
	        /*暂时不上传：common.ajaxFunc("/sqzc/app/common/jslog/savelog", data, "json", function(a) {
	        });*/
	    },0);
    }catch(err){
    	
    }
    return false;
};

//退出（有在main.js里引用，下面也引用）
function __logOut()
{
	//window.location.href="/sqzc/app/platform/login/logout";
	//关闭所有层，并跳转
	common.closeAllModule(__logOut_Callback);
	//common.win_app_open("http://www.baidu.com",abc);
	__logOut_Callback();
}
function __logOut_Callback()  //退出回调函数
{
	common.win_open("/sqzc/app/platform/login/logout");
}


$(function(){
	
	window.app_init_headmore && window.app_init_headmore();
	
	var isCookieNull = getCookie("_view_time");
	if(isCookieNull == null ||isCookieNull.trim() == ""){
		//cookie为空后台访问量加一
		addReadCount();
		setCookie("_view_time","_view_time");	//设置cookie
	}else{  //cookie不为空，不作处理
	
	}
	
})
 
function getCookie(c_name) {  //读取cookies 
    var arr, reg = new RegExp("(^| )" + c_name + "=([^;]*)(;|$)");

    if (arr = document.cookie.match(reg))

        return unescape(arr[2]);
    else
        return ""; 

}

//设置cookie
function setCookie(c_name,c_value) {   
    document.cookie = c_name + "=" + escape(c_value) + ";path=/";  //让 cookie 在根目录下,这样不管是哪个子页面创建的 cookie，所有的页面都可以访问到了，这样才能获取最新的cookie

}

function addReadCount(){ //后台访问量加一
	var params ={};
	var url = "/sqzc/m/pf/count/addCount";
	common.ajaxFunc(url,params,"json",function(result){
		if (result.success) {
			//alert(result.msg);
		}
	});
}

</script>

<script type="text/javascript">
	function createPageHTML(a,b,c,d){
		
	}
</script>
<script type="text/javascript">
	//兼容手机端
	if(browserRedirect()){
		//$('.footer_app,.newPhoneHead').remove();
  		$('.header-title-cont').addClass('showTab');
	}else{
		$('#biaoqianTab').remove();
	}
	
	// 获取标签文本
	function getfiltrate() {
		var div_subscribe = $(".zcLable");
		var keywords = "";
		var keyWordObjs = div_subscribe.find(".beCheckedSpan");  //获取到选中的
		for(var i=0;i<keyWordObjs.length;i++){
			if(keywords!=""){
				keywords+=",";
			}
			keywords+= $(keyWordObjs[i]).text();
		}
		return keywords;
	}
	// 保存订阅
	function saveSubscribe() {
		var keywords = getfiltrate();
		if ('' == keywords) {
			layer.msg("请选择标签", null, null);
			return;
		}
		var url = '/sqzc/m/pf/user/saveSubscribe';
		var data = {'keywords':keywords};
		common.ajaxFunc(url,data,'json',function(result){
			if (result.success) {
				layer.msg("订阅成功", null, null);
			}
		});
	}
	// 获取相关咨询
	function getRelatedConsult() {
		var url = '/sqzc/m/cms/consult/getConsultRelated';
		var title = $("#span_title").text();
		if(browserRedirect()){
			title = $(".top_title").text();
		}
		var data = {'pageNumber': 1,'pageSize': 2, 'title': title};
		common.ajaxFunc(url,data,'json',function(result){
			if (result.success) {
				var list = result.data;
				var li = '';
				if (list == null || list.length == 0) {
					li += '<li class="no-data">';
					li += '暂无咨询数据';
					li += '</li>';
					$('.relatedConsult').append(li);
					return;
				}
				$.each(list, function (i, item){
					li += '<li>';
					li += '<div class="questionsCol">';
					li += '<span class="questionsType"><img src="/sqzc/images/askImg.png" alt=""></span>';
					li += '<p>'+ item.consult_content +'</p>';
					li += '</div>';
					li += '<div class="questionsCol">';
					li += '<span class="questionsType answers"><img src="/sqzc/images/answers.png" alt=""></span>';
					if (item.reply_content != null && item.reply_content != '') {
						li += '<p>'+ item.reply_content +'</p>';
					} else {
						li += '<p>&nbsp;</p>';
					}
					li += '</div>';
					li += '<div class="diagramListBtn questionsColBtn">';
					li += '<span class="RlistFrom nowrap">咨询者：<i class="asker"></i>'+ item.consultant_name +'</span>';
					li += '<span class="RlistTime">'+ item.consult_timeStr +'</span>';
					li += '</div>';
					li += '</li>';
				});
				$('.relatedConsult').append(li);
			}
		});
	}
	</script>
	<script>
	var form;
	layui.use(['element','layer','table','form'], function(){
			
	    $(function () {
	    	var element = layui.element;
	    	form = layui.form;
	        // 筛选 政策标签,选择订阅标签
	        $('.zcLable').on('click', 'span', function () {
	            $(this).toggleClass('beCheckedSpan');
	            var Label = $(this).text();
	            //url = encodeURI("/sqzc/m/cms/policy/toSearchLabel?Label="+Label);
	            window.open("/sqzc/m/cms/policy/toSearchLabel?Label="+encodeURI(encodeURI(Label)));
	        });
	        
	        getPolicyinfo(); //获取政策详细内容
	        getCollectStatus();
	        getQuestionnaireInfo();//获取调查问卷
	    })
	    
	});
	
	$(function(){
		
		getPolicyLabel(); //获取政策标签
		
	});
	function loadPolicy(){
		if(resultInfo){
			$(".phoneContTitle .top_title").text(resultInfo.title);
			$(".phoneContTitle .policySource").text(resultInfo.publisher); //发布机构
			$(".phoneContTitle .policyPublishDate").text(resultInfo.publishDate); //时间
		}
	}
	
	var resultInfo;//政策详细信息
	//获取政策详细信息
    function getPolicyinfo(){
		var id = "nfsheqizcyw12220432";
    	params = {"id":id};
    	var url = "/sqzc/m/cms/policy/getPolicyinfo";
    	common.ajaxFunc(url,params,"json",function(result){
    		if(result.success){
    			resultInfo = result.data;
    			console.log(resultInfo)
    			//政府标签
    			/* $('meta[name=ArticleTitle]').attr('content',result.data.title);
    			$('meta[name=PubDate]').attr('content',result.data.PubDate);
    			$('meta[name=ContentSource]').attr('content',result.data.source); */
    			if(result.data.jumpUrl) {
    				$('#jumpUrl').attr('href',result.data.jumpUrl);
    				$('#jumpUrl').attr('target',"_blank");
    			}else {
    				$('#jumpUrl').attr('href',"javascript:;");
    				$('#jumpUrl').attr('target',"");
    			}
    			
    			
    			
    			$("title").html(result.data.title);
        		$("#policypublisher").html(result.data.publisher); //发布机构
        		$("#policyDate").html(result.data.date); //成文日期
        		$("#span_title").html(result.data.title); //标题
        		$(".top_title").html(result.data.title);//顶部标题
        		
        		//loadPolicyRelatedTable(result.data.title); //加载政策相关
        		//loadPolicyAnalysisRelatedTable(result.data.title); //加载政策解读相关
        		relatedPolicyAnalysisRelatedInfo(result.data.title);//加载部门解读
        		if(!browserRedirect()){
        			shareInit(result.data.title, null, null, null); //加载分享js
        		}
        		loadRelatedRes();//info接口获取相关资源
        		$(".policyPublishDate").text(result.data.publishDate); //发布时间
        		$(".policySource").text(result.data.publisher);//来源
        		$("#policyIssuedNum").text(result.data.issuedNum); //文号
        		$("#policyIssuedNum2").text(result.data.issuedNum); //文号
        		$("#policyIdentifier").text(result.data.identifier); //索引
        		$("#policyClassify").text(result.data.classify); //分类
        		
        		$("#policytitle2").text(result.data.title);
        		$("#policytitle2").attr("title",result.data.title);
        		var htmlContent = result.data.htmlContent;
        		if(htmlContent){
	        		htmlContent = htmlContent.replace(/\<img/g, '<img onerror="hiddenImg(this)" style="width:100%;height:auto;"');
        		}
        		if(htmlContent){//图片通过涉企中转，防止https与http冲突
        			htmlContent = htmlContent.replace(/\<table/g, '<table style="max-width:100%;width:100%;"');
	        		htmlContent = htmlContent.replace(/\<div/g, '<div style="width:100%;height:auto;"');
        			htmlContent.replace(/<img [^>]*>/gi, function (match) {
        				  //console.log(match);
        				  if(match && match.indexOf('ipBan') == -1){
        					  var html = "";
        					  if(match.indexOf('images254') == -1 && match.indexOf('images250') == -1 && match.indexOf('http://sqzc.gd.gov.cn:80/') == -1){
	        					  html = match.replace(" src=\""," src=\"/sqzc/m/cms/common/getPictureByUrl?libNum=policy_"+ id +"&url=");
        					  }else{
        						  html = match.replace(" src=\""," src=\"/sqzc/m/cms/common/getIampPictureByUrl?url=");
        						  html = html.replace('sqzc.gd.gov.cn:80','127.0.0.1:80');
        					  }
        					  htmlContent = htmlContent.replace(match,html);
        				  }
        			});
        		}
        		$(".articleContent").html(htmlContent); //正文内容 
        		$("#abstractText").html(result.data.abstractText); //摘要
        		$("#abstractText").attr('title',result.data.abstractText);
        		$("#fileUrl").attr('href', result.data.fileUrl); // 附件
       			if(result.data.fileUrlList){
           			var fileUrlList = result.data.fileUrlList;
           			var fileUrlListHtml = "";
   	        		if(fileUrlList && fileUrlList.length >0){
   	        			for(var i=0;i < fileUrlList.length ; i++){
   	        				var fileInfo = fileUrlList[i];
   	        				var fileUrl = fileInfo.url;
   	        				var fileTitle = fileInfo.name;
   	        				fileUrlListHtml += '<p class="accessoryText">附件：<i><a target="_blank" href="'+fileUrl+'" >'+fileTitle+'</a></i></p>';
   	        			}
	        			$(".fileUrlList").append(fileUrlListHtml);
    	        		$(".fileUrlList").removeClass('none');
   	        			
   	        		}else{
   	        			//fileUrlListHtml += '<p class="no-data">暂无附件数据</p>';
   	        			$(".fileUrlList").append(fileUrlListHtml);
    	        		$(".fileUrlList").removeClass('none');
   	        		}
           		}
           		//支撑平台附件
           		if(result.data.fileInfos){
   	        		var fileInfos = JSON.parse(result.data.fileInfos);
   	        		if(fileInfos && fileInfos.length >0){
   	        			console.log(fileInfos);
   	        			var fileInfosHtml = "";
   	        			for(var i=0;i < fileInfos.length ; i++){
   	        				var fileInfo = fileInfos[i];
   	        				var fileId = fileInfo.id.replace(" ","");
   	        				var fileTitle = fileInfo.title;
   	        				var fileExt = fileInfo.fileExt;
   	        				var playButton = "";
   	        				if(fileExt == "mp4"){
   	        					playButton = ' <button> 播放 </button> ';
   	        				}
   	        				fileInfosHtml += '<p class="fileInfo accessoryText" fileId="'+fileId+'" fileTitle="'+fileTitle+'" fileExt="'+fileExt+'"><i><a href="" onclick="return false;">'+fileTitle+'.'+fileExt+'</a></i>'+ playButton +'</p>';
   	        			}
   	        			$(".fileUrlList").append(fileInfosHtml);
   	        			$(".fileUrlList").removeClass('none');
   	        		}
           		}
        		
        		
        		getRelatedConsult(); // 获取相关咨询
        		
    		}
    	});
    }
    $(".fileUrlList,.fileInfos").on("click",".fileInfo i",function(){
		var id = $(this).parent('.fileInfo').attr("fileId");
		var resId = "nfsheqizcyw12220432";
		var libNum = "originalpolicy";
		params = "id="+id+"&resId="+resId+"&libNum="+libNum;
		var url = "/sqzc/m/cms/common/fileDownload?"+params;
		if(browserRedirect()){
			window.location.href = encodeURI(url.replace(/%/g,'%25'));
		}else{
			window.open(encodeURI(url.replace(/%/g,'%25')));
		}
	})
	
	$(".fileUrlList,.fileInfos").on("click",".fileInfo button",function(){
		var that = $(this).parent('.fileInfo');
		var id = that.attr("fileId");
		var fileTitle = that.attr("fileTitle");
		var resId = "nfsheqizcyw12220432";
		var libNum = "originalpolicy";
		var url =  "/sqzc/m/cms/common/video/"+libNum+"-"+resId+"-attach-"+id+"?title="+fileTitle;
		if(browserRedirect()){
			window.location.href = encodeURI(url.replace(/%/g,'%25'));
		}else{
			window.open(encodeURI(url.replace(/%/g,'%25')));
		}
	})
	
	//获取政策标签,有问题
    function getPolicyLabel(){
		var id = "nfsheqizcyw12220432";
    	params = {"id":id};
    	var url = "/sqzc/m/cms/policy/getPolicylabel";
    	common.ajaxFunc(url,params,"json",function(result){
    		if(result.success){
        		var data = result.data;
        		var span = "";
        		if(browserRedirect()){
        			if(!data.length){
        				$(".shaixuan").hide();
        				return;
        			}
        			
        		}
        		for (var i = 0; i < data.length; i++) {
					for(var key in data[i])
					{
						var parentString = data[i][key];
						if(parentString.indexOf("体裁") <0){
							/* if(i == 0){
								span += "<span class='beCheckedSpan'>"+key+"</span>";
							}else{
								span += "<span>"+key+"</span>";
							} */
							if (i < 12) {
								span += "<span>"+key+"</span>";
							} else {
								break;
							}
						}
					}
				}
        		$(".zcLable").append(span);
    		}
    	});
    }
	
	// 获得政策相关--例外
	function loadPolicyRelatedTable2(title){
		//var whereJson = {"title":title,pageSize:3};
		var whereJson = {"id":"nfsheqizcyw12220432"};
		layuis.tableRenderSimp(
				 'zcjdTable',
				 '/sqzc/m/cms/policyanalysis/getPolicyPolicyanalysisRelated',
				 [ //表头
		          {field: 'title', title: '标题', align:'left'}
		          ,{field: 'publishDate', title: '发布时间', width:180,
		        	  sort: true,align:'right'}
		      	 ],
		      	whereJson,
		         null,
				 function(res, curr, count){
		        	 var id = null;
		        	 var dataLength = res.data==null?0:res.data.length;
		        	 if (dataLength == 0) {
		        		 $('.zcxgList').css('display', 'none');
		        	 }
				     $('td[data-field="title"]').each(function (i) {
				         $(this).find('.layui-table-cell').click(function () {
				             id = res.data[i].id;
				             var belong = res.data[i].belong;
							 if (belong == 'projectdeclaration') {
								 toProjectInfo(id);
							 } 
							 if (belong == 'informationdisclosure') {
								 toPublicityInfo(id);
							 }
				             //toPolicyInfo(id);
				         })
				     })
				 },
				 false,
				 false,
				 {"page":false} //其他参数
			);
	}
	// 获得政策相关--例外
	function loadPolicyRelatedTable(title){	
		var timestamp = Date.parse(new Date());
		var url = '/sqzc/m/cms/policyanalysis/getPolicyPolicyanalysisRelated?'+timestamp;
		var data = {"id":"nfsheqizcyw12220432"};
		common.ajaxFunc(url,data,'json',function(result){
			if (result.success) {
				var list = result.data;
				var li = '';
				if (list == null || list.length == 0) {
					li += '<li class="no-data">';
					li += '暂无';
					li += '</li>';
					$('#relatedPolicy').append(li);
					$('#relatedPolicy').parent().hide();
					return;
				}
				$.each(list, function (i, item){
					li += '<li class="pointer">';					
				    var belong = item.belong;
					if (belong == 'projectdeclaration') {				
						li += '<p onclick="toProjectInfo(\''+item.id+'\');">' + item.title + '</p>';
					} 
					if (belong == 'informationdisclosure') {
						li += '<p onclick="toPublicityInfo(\''+item.id+'\');">' + item.title + '</p>';
					}
					li += '<div class="diagramListBtn">';
					li += '<span class="RlistFrom nowrap">' + item.publisher + '</span>';
					li += '<span class="RlistTime">' + item.publishDate + '</span>';
					li += '</div>';
					li += '</li>';
				});
				$('#relatedPolicy').parent().show();
				$('#relatedPolicy').append(li);
			}
		});
	}
	var relatedAnalysisInfo;
	//加载相关部门解读的详细信息
	function loadRelatedAnalysisInfo(){
		//console.log(relatedAnalysisInfo);
		$(".relatedAnalysis").css("display","");
		if(browserRedirect()){
			$(".phonePolicyanalysis").css("display","");
			$(".phoneContTitle .top_title").text(relatedAnalysisInfo.title);
			$(".phoneContTitle .policySource").text(relatedAnalysisInfo.publisher); //发布机构
			$(".phoneContTitle .policyPublishDate").text(relatedAnalysisInfo.publishDate); //时间
			$("#policyanalysisinfo").html(relatedAnalysisInfo.htmlContent); //内容
		}else{
			$("#span_title2").text(relatedAnalysisInfo.title); //时间
			$("#policyanalysisupdateDate").text(relatedAnalysisInfo.publishDate); //时间
			$("#policyanalysissource").text(relatedAnalysisInfo.publisher); //来源
			$("#policyanalysisinfo").html(relatedAnalysisInfo.htmlContent); //内容
			//$("#policyanalysissource").val(relatedAnalysisInfo.publisher); //发布机构
		}
	}
	//获取相关部门解读的详细信息
	function relatedPolicyAnalysisRelatedInfo(title){
		var id = "nfsheqizcyw12220432";
		if(id == '5c7f980f9932858833883b84') { // 为演示写死
			var params = {"id":"5c7f98939932858833883b85"};
	    	var url = "/sqzc/m/cms/policyanalysis/getPolicyanalysisinfo";
	    	common.ajaxFunc(url,params,"json",function(result){
	    		if(result.success){
	    			relatedAnalysisInfo = result.data;
	    			loadRelatedAnalysisInfo();
	    		}	
	    	});
		} else {
			/* var timestamp = Date.parse(new Date());
			var url = '/sqzc/m/cms/policyanalysis/getPolicyRelated?'+timestamp;
			var data = {'title': title,"catalogyName":"1"};//catalogyName:1-部门解读，2-媒体解读
			common.ajaxFunc(url,data,'json',function(result){
				if (result.success) {
					var list = result.data;
					if(list != null && list.length > 0 ){
						var params = {"id":list[0].id};
				    	var url = "/sqzc/m/cms/policyanalysis/getPolicyanalysisinfo";
				    	common.ajaxFunc(url,params,"json",function(result){
				    		if(result.success){
				    			relatedAnalysisInfo = result.data;
				    			loadRelatedAnalysisInfo();
				    		}	
				    	});
					}
				}
			}); */
			var url = '/sqzc/m/cms/common/getRelatedRes?';
			var data = {'id': id,"libNum":"originalpolicy"};
			common.ajaxFunc(url,data,'json',function(result){
				if (result.success) {
					var relatedPolicyAnalysis1 = result.data[2].relatedPolicyAnalysis1;
					if(relatedPolicyAnalysis1 != null && relatedPolicyAnalysis1.length > 0 ){
						var params = {"id":relatedPolicyAnalysis1[0].id};
				    	var url = "/sqzc/m/cms/policyanalysis/getPolicyanalysisinfo";
				    	common.ajaxFunc(url,params,"json",function(result){
				    		if(result.success){
				    			relatedAnalysisInfo = result.data;
				    			relatedAnalysisInfo.id = relatedPolicyAnalysis1[0].id;
				    			if(browserRedirect()){
				    				$(".phonePolicyanalysis").css("display","");
				    			}else{
					    			loadRelatedAnalysisInfo();
				    			}
				    		}	
				    	});
					}
				}
				/* relatedAnalysisInfo = {
						"title":"test",
						"publishDate":"2020-07-02",
						"source":"本网",
						"publisher":"本网",
						"htmlContent":"test"
						} 
				$(".phonePolicyanalysis").css("display",""); */
				//loadRelatedAnalysisInfo(); 
			});
		}
		if (typeof(relatedAnalysisInfo)=="undefined"){
			$('.relatedAnalysis').hide();
		}else{
			$('.relatedAnalysis').show();
		}		
	}
	// 相关媒体解读
	function loadPolicyAnalysisRelatedTable(title) {
		var timestamp = Date.parse(new Date());
		var url = '/sqzc/m/cms/policyanalysis/getPolicyRelated?'+timestamp;
		var data = {'title': title,"catalogyName":"2"};//catalogyName:1-部门解读，2-媒体解读
		console.log(url);
		common.ajaxFunc(url,data,'json',function(result){
			if (result.success) {
				var list = result.data;
				var li = '';
				if (list == null || list.length == 0) {
					li += '<li class="no-data">';
					li += '暂无';
					li += '</li>';
					$('#relatedPolicyAnalysis').append(li);
					$('#relatedPolicyAnalysis').parent().hide();
					return;
				}
				$.each(list, function (i, item){
					li += '<li class="pointer">';
					li += '<p onclick="toPolicyAnalysisInfo(\''+item.id+'\');">' + item.title + '</p>';
					li += '<div class="diagramListBtn">';
					li += '<span class="RlistFrom nowrap">' + item.publisher + '</span>';
					li += '<span class="RlistTime">' + item.publishDate + '</span>';
					li += '</div>';
					li += '</li>';
				});
				$('#relatedPolicyAnalysis').parent().show();
				$('#relatedPolicyAnalysis').append(li);
			}
		});
	}

	//info接口获取所有相关资源
	function loadRelatedRes() {
		var timestamp = Date.parse(new Date());
		var url = '/sqzc/m/cms/common/getRelatedRes?';
		var data = {'id': "nfsheqizcyw12220432","libNum":"originalpolicy"};//catalogyName:1-部门解读，2-媒体解读
		console.log(url);
		common.ajaxFunc(url,data,'json',function(result){
			console.log(result.data);
			if (result.success) {
				//相关政策
				var relatedPolicy = result.data[0].relatedPolicy;
				var li = '';
				if (relatedPolicy == null || relatedPolicy.length == 0) {
					li += '<li class="no-data">';
					li += '暂无';
					li += '</li>';
					$('#relatedPolicy').append(li);
					$('#relatedPolicy').parent().hide();
				}else{
					$.each(relatedPolicy, function (i, item){
						li += '<li class="pointer">';
						li += '<p onclick="toPolicyInfo(\''+item.id+'\');">' + item.title + '</p>';
						li += '<div class="diagramListBtn">';
						li += '<span class="RlistFrom nowrap">' + item.publisher + '</span>';
						li += '<span class="RlistTime">' + item.publishDate + '</span>';
						li += '</div>';
						li += '</li>';
					});
					$('#relatedPolicy').parent().show();
					$('#relatedPolicy').append(li);
				}
				//加载相关媒体解读
				var relatedPolicyAnalysis = result.data[3].relatedPolicyAnalysis2;
				var li = '';
				if (relatedPolicyAnalysis == null || relatedPolicyAnalysis.length == 0) {
					li += '<li class="no-data">';
					li += '暂无';
					li += '</li>';
					$('#relatedPolicyAnalysis').append(li);
					$('#relatedPolicyAnalysis').parent().hide();
				}else{
					$.each(relatedPolicyAnalysis, function (i, item){
						li += '<li class="pointer">';
						li += '<p onclick="toPolicyAnalysisInfo(\''+item.id+'\');">' + item.title + '</p>';
						li += '<div class="diagramListBtn">';
						li += '<span class="RlistFrom nowrap">' + item.publisher + '</span>';
						li += '<span class="RlistTime">' + item.publishDate + '</span>';
						li += '</div>';
						li += '</li>';
					});
					$('#relatedPolicyAnalysis').append(li);
					$('#relatedPolicyAnalysis').parent().show();
				}

				//显示项目
				var relatedProjects = result.data[1].relatedProject;
				if(relatedProjects != null && relatedProjects.length > 0) {
					var li = "";
					$.each(relatedProjects, function (i, item){
						li += '<li class="pointer">';					
					    var belong = item.libNum;
						if (belong == 'projectdeclaration') {				
							li += '<p onclick="toProjectInfo(\''+item.id+'\');">' + item.title + '</p>';
						} 
						if (belong == 'declarationnotice') {
							li += '<p onclick="toDeclarationInfo(\''+item.id+'\');">' + item.title + '</p>';
						}
						li += '<div class="diagramListBtn">';
						li += '<span class="RlistFrom nowrap">' + item.publisher + '</span>';
						li += '<span class="RlistTime">' + item.publishDate + '</span>';
						li += '</div>';
						li += '</li>';
						
					}); 
					$('#relatedProject').append(li);
					$('#relatedProject').parent().show();
				} else {
					li = '';
					li += '<li class="no-data">';
					li += '暂无';
					li += '</li>';
					$('#relatedProject').append(li);
					$('#relatedProject').parent().hide();
				}
				
				
			}
		});
	}

</script>

<script type="text/javascript">
	// 政策详情
	function toPolicyInfo(id)
	{
	 window.open('/sqzc/m/cms/policy/policyinfo/'+id);
	}
	
	// 项目申报详情
	function toProjectInfo(id){
		window.open('/sqzc/m/cms/project/toProjectInfo/'+id);
	}

	function toDeclarationInfo(id) {
		window.open('/sqzc/m/cms/project/toDeclarationInfo/'+id);
	}
	
	// 项目公示，申办通知
	function toPublicityInfo(id){
		window.open('/sqzc/m/cms/publicity/publicityInfo?id='+id);
	}
	
 	//政策解读详情页面
	function toPolicyAnalysisInfo(id){
		window.open('/sqzc/m/cms/policyanalysis/policyanalysisinfo/'+id);
	}
 	
	//更多相关项目
 	function moreProject(){
		window.open("/sqzc/m/cms/project");
	}
 	//更多相关政策
 	function morePolicy(){
		window.open("/sqzc/m/cms/policy");
	}
	//更多解读
 	function morePolicyAnalysis(){
		window.open("/sqzc/m/cms/policyanalysis");
	}
	//检查登录
 	function checkLogin(){
 			var isLogin = 'false';
 			console.log(isLogin)
 			if(isLogin == 'true'){
 				return true;
 			}else{
 				var msg = '请你先登录系统!';
 				layer.msg(msg,null,null);
 				return false;
 			}
 		}
	//留言
	function toleaveMessage1(){
		/* if(!checkLogin()){
			return;
		} */
		var msg = '请你先登录系统';
		if(!checkLogin()){
			layer.msg(msg,null,null);
			return;
		}else{
			msg = '功能建设中，暂未开放';
			layer.msg(msg,null,null);
			return;
		}
		var msg = '功能建设中，暂未开放';
		layer.msg(msg,null,null);
		return;
		var id = "nfsheqizcyw12220432";
		var title = $("#span_title").text();
		//var dept_name = $("#policypublisher").text();
		var dept_name = $(".policySource").text();
		var type = "originalpolicy";
		var url = "/sqzc/m/cms/consult/toLeaveMessage?title="+title+"&id="+id+"&type="+type+"&dept_name="+dept_name;
		url = encodeURI(encodeURI(url.replace(/%/g,'%25')));
		window.open(url);
	}
	function toleaveMessage2(){
		var hrefVal = $("#jumpUrl").attr("href");
		if(!hrefVal || hrefVal == "" || hrefVal == "#" || hrefVal == "javascript:;") {
			layer.alert("抱歉,该政策暂不支持咨询");
		}
		
		/* var siteId = resultInfo.siteId;
		var title = resultInfo.title;
		var issuedNum = resultInfo.issuedNum;
		if(siteId){
			var zxzc = "";
			if(title){
				
			}
			if(issuedNum){
				
			}else{
				issuedNum = "";
			}
			var url = '/sqzc/m/cms/common/getJumpUrl';
			var data = {"siteId":siteId,"title":title,"issuedNum":issuedNum};
			common.ajaxFunc(url,data,'json',function(result){
				console.log(result);
				if(result && result.success){
					var jumpUrl = result.data.url;
					//var site_id = result.data.site_id;
					//var zxzc = result.data.zxzc;
					//var _token = result.data.token;
					//window.open(jumpUrl+"?site_id="+site_id+"&zxzc="+encodeURI(zxzc)+"&_token="+_token);
					//window.open(encodeURI(jumpUrl));
					//window.open(encodeURI(decodeURI(jumpUrl)));
					jumpUrl = decodeURI(jumpUrl);
					window.open(encodeURI(jumpUrl));
				}else{
					layer.alert("抱歉,该政策暂不支持咨询");
				}
			})
		}else{
			layer.alert("抱歉,该政策暂不支持咨询");
		} */
	}
	// 更多咨询
	function toAskAnswers() {
		var title = $("#span_title").text();
		//var url = '/sqzc/m/cms/consult/toAskAnswers?title='+title;
		var url = '/sqzc/m/cms/ai/toAIConsultList?title='+title;
		url = encodeURI(encodeURI(url.replace(/%/g,'%25')));
		window.open(url);
	}
</script>
<script type="text/javascript">
var policyCollectStatus = null;
//查询是否收藏政策原文
function getCollectStatus() {
	$('.beCollect').empty();
	$('.diagramRight').show();
	$('.diagramLeft').css('width','790px');
	if(policyCollectStatus == null){
		var url = '/sqzc/m/pf/collect/getCollect';
		var data = {'collect_id':'nfsheqizcyw12220432'};
		common.ajaxFunc(url,data,'json',function(result){
			if (result.success) {
				var flag = result.data;
				var text = '';
				policyCollectStatus = flag;
				if (flag) {
					text = '<i class="iconfont beCollectIcon">&#xe747;</i> 已收藏';
					$('.beCollect').attr('collectstatus','has');
				} else {
					text = '<i class="iconfont beCollectIcon">&#xe748;</i> 收藏';
					$('.beCollect').attr('collectstatus','not');
				}
				$('.beCollect').append(text);
			}
		});
	}else{
		if (policyCollectStatus) {
			text = '<i class="iconfont beCollectIcon">&#xe747;</i> 已收藏';
			$('.beCollect').attr('collectstatus','has');
		} else {
			text = '<i class="iconfont beCollectIcon">&#xe748;</i> 收藏';
			$('.beCollect').attr('collectstatus','not');
		}
		$('.beCollect').append(text);
	}
}
var policyanalysisCollectStatus = null;
//查询是否收藏政策解读
function getCollectStatus2() {
	$('.beCollect').empty();
	$('.diagramRight').hide();
	$('.diagramLeft').css('width','100%');
	if(policyanalysisCollectStatus == null){
		var url = '/sqzc/m/pf/collect/getCollect';
		var data = {'collect_id':relatedAnalysisInfo.id};
		common.ajaxFunc(url,data,'json',function(result){
			if (result.success) {
				var flag = result.data;
				var text = '';
				policyanalysisCollectStatus = flag;
				if (flag) {
					text = '<i class="iconfont beCollectIcon">&#xe747;</i> 已收藏';
					$('.beCollect').attr('collectstatus','has');
				} else {
					text = '<i class="iconfont beCollectIcon">&#xe748;</i> 收藏';
					$('.beCollect').attr('collectstatus','not');
				}
				$('.beCollect').append(text);
			}
		});
	}else{
		if (policyanalysisCollectStatus) {
			text = '<i class="iconfont beCollectIcon">&#xe747;</i> 已收藏';
			$('.beCollect').attr('collectstatus','has');
		} else {
			text = '<i class="iconfont beCollectIcon">&#xe748;</i> 收藏';
			$('.beCollect').attr('collectstatus','not');
		}
		$('.beCollect').append(text);
	}
}
// 收藏或取消收藏
function saveOrCancelCollect() {
	if(!checkLogin()){
		return;
	} 
	var url = '/sqzc/m/pf/collect/saveOrCancelCollect';
	var collectstatus = $('.beCollect').attr('collectstatus');
	var type = '';
	var res;
	
	var original_url = window.location.href;
	var title = "";
	var publish_date = "";
	var data = {};
	if (collectstatus == 'has') {
		type = 'cancel';
	}
	var libName = $('.diagramLeft li.layui-this').text();
	if(libName == "政策原文"){
		title = $("#span_title").text();
		publish_date = $(".policyPublishDate").text();
		if (collectstatus == 'not') {
			type = 'save';
			res = '{';
		}
		data = {'collect.collect_title': title,'collect.collect_id':'nfsheqizcyw12220432',
				'collect.original_url':original_url,'collect.collect_type':'originalpolicy',
				'collect.publish_date':publish_date,'type':type,'data':res};
	}else if(libName == "政策解读"){
		title = $("#span_title2").text();
		publish_date = $("#policyanalysisupdateDate").text();
		if (collectstatus == 'not') {
			type = 'save';
			res = '{';
		}
		original_url = "/sqzc/m/cms/policyanalysis/policyanalysisinfo/"+ relatedAnalysisInfo.id;
		data = {'collect.collect_title': title,'collect.collect_id':relatedAnalysisInfo.id,
				'collect.original_url':original_url,'collect.collect_type':"policyinterpretation",
				'collect.publish_date':publish_date,'type':type,'data':res};
	}
	console.log(data);
	common.ajaxFunc(url,data,'json',function(result){
		if (result.success) {
			policyCollectStatus = null;
			policyanalysisCollectStatus = null;
			$('.beCollect').empty();
			var flag = result.data;
			if (flag) {
				text = '<i class="iconfont beCollectIcon">&#xe747;</i> 已收藏';
				$('.beCollect').attr('collectstatus','has');
				$('.beCollect').append(text);
				layer.msg("收藏成功", null, null);
			} else {
				text = '<i class="iconfont beCollectIcon">&#xe748;</i> 收藏';
				$('.beCollect').attr('collectstatus','not');
				$('.beCollect').append(text);
				layer.msg("取消成功", null, null);
			}
		} else {
			if(type =='save'){
				layer.msg("收藏失败", null, null);
			}else if(type =='cancel'){
				layer.msg("取消失败", null, null);
			}
		}
	});
}

//收藏或取消收藏(手机端)
function saveOrCancelCollectForPhone() {
	if(!checkLogin()){
		return;
	}
	var url = '/sqzc/m/pf/collect/saveOrCancelCollect';
	var collectstatus = $('.beCollect').attr('collectstatus');
	var type = '';
	var res;
	
	var original_url = window.location.href;
	if (collectstatus == 'has') {
		type = 'cancel';
	}
	var title = $('.phoneContTitle .top_title').text();
	var publish_date = $(".phoneContTitle .policyPublishDate").text();
	
	if (collectstatus == 'not') {
		type = 'save';
		res = '{';
	}
	var data = {'collect.collect_title': title,'collect.collect_id':'nfsheqizcyw12220432',
			'collect.original_url':original_url,'collect.collect_type':'originalpolicy',
			'collect.publish_date':publish_date,'type':type,'data':res};
	console.log(data);
	common.ajaxFunc(url,data,'json',function(result){
		if (result.success) {
			policyCollectStatus = null;
			policyanalysisCollectStatus = null;
			$('.beCollect').empty();
			var flag = result.data;
			if (flag) {
				text = '<i class="iconfont beCollectIcon">&#xe747;</i> 已收藏';
				$('.beCollect').attr('collectstatus','has');
				$('.beCollect').append(text);
				layer.msg("收藏成功", null, null);
			} else {
				text = '<i class="iconfont beCollectIcon">&#xe748;</i> 收藏';
				$('.beCollect').attr('collectstatus','not');
				$('.beCollect').append(text);
				layer.msg("取消成功", null, null);
			}
		} else {
			if(type =='save'){
				layer.msg("收藏失败", null, null);
			}else if(type =='cancel'){
				layer.msg("取消失败", null, null);
			}
		}
	});
}

$(function(){
	if(!browserRedirect()){
		$(window).scroll(function(){
			viewW = $(this).width(); //可见宽度
	        viewH =$(this).height(),//可见高度
	        contentH = $(".gvmHeader").get(0).scrollHeight+$(".contentTop").get(0).scrollHeight+100;//内容高度
	        contentC = $(".indexHome-cont").get(0).scrollHeight;
	        contentF = $(".gvmFoot ").get(0).scrollHeight;
	        var diagramRightH = $('.diagramRight').height();
	        var bodyScrollHeight = $("body ").get(0).scrollHeight;
	        scrollTop =$(this).scrollTop();//滚动高度
	        var footTop = $(".gvmFoot").offset().top-$(window).scrollTop();//页尾距可视窗口高度
	        if (scrollTop>contentH && diagramRightH < viewH - 20 && diagramRightH < footTop){
	        	$('.diagramRight').addClass('diagramRightFit');
				if (scrollTop > (contentC - $(".gvmFoot ").height())){
					$('.diagramRightFit').css('bottom',($(".gvmFoot ").height()+10)+'px');
					$('.diagramRightFit').css('top','auto');
				} else {
					$('.diagramRightFit').css('top','0px');
					$('.diagramRightFit').css('bottom','auto');
				}
				if(viewW > 1200) {
					var right = (viewW - 1200)/2;
					$('.diagramRightFit').css('right',right + 'px');
				} else {
					$('.diagramRightFit').css('left','820px');
				}
	        }else{
				$('.diagramRight').removeClass('diagramRightFit');
	        }
	    });
	}
	
}) 
//调查问卷相关功能
//问卷详情
function getQuestionnaireInfo(){
    var url = "/sqzc/m/dept/questionnaire/getQuestionnaireInfo";
    var data = {"resId":'nfsheqizcyw12220432',"type":"use"}
	common.ajaxFunc(url,data,'json',function(result){
		console.log(result);
		if(result.success){
			var data = result.data;
			if(data){
				var questionnaireId = data.id;
				var questionNumber = data.questionNumber;
				var title = data.title;
				if(title){
					var html = '<a style="text-decoration: none;" href="/sqzc/m/dept/questionnaire/toQuestionnaireInfo?id='+questionnaireId+'" target="_blank">'
					+ title + '</a>';
					$('#questionnaire').append(html);
					$('.questionnaireDiv').removeClass('none');
				}
				/* 
				$('#questionnaire').attr("questionnaireId",questionnaireId);
				$('#questionnaire').attr("current",0);
				$('#questionnaire').attr("questionNumber",questionNumber);
				$('.questionnaireDiv .moreText ').text("共 "+questionNumber+" 题");
				$('.questionnaireDiv').removeClass('none');
				var questions = data.questions;
				for(var i=0;i<questions.length;i++){
					var question = questions[i];
					var questionId = question.questionId;
					var questionContent = question.question_content;
					var questionType = question.type;
					var serialNumber = i+1;
					var rate = serialNumber + "/"+ questionNumber;
						
					if(questionType == "问答"){
						var html = '<div class="layui-form-item layui-form-text none" sort="'+ i +'">'
							+'<div class="question" questionId="'+ questionId +'" type="'+ questionType +'"><div class="questionTitle">'+ serialNumber +'、'+ questionContent +'</div><span>('+ rate +')</span></div>'
							+'<div class="wendati"><textarea placeholder="请输入内容" class="layui-textarea"></textarea></div></div>'
						$('#questionnaire form').append(html);
					}else if(questionType == "多选"){
						var options = question.options;
						var otionsHtml = "";
						for(var j=0;j<options.length;j++){
							var option = options[j];
							var content = option.content;
							var id = option.id;
							otionsHtml += '<input type="checkbox" optionId="'+ id +'" lay-skin="primary"  name="'+ questionId +'" value="'+ id +'" title="'+ content +'">'
						}
						var html = '<div class="layui-form-item none" sort="'+ i +'" pane="">'
				  		+'<div class="question" questionId="'+ questionId +'" type="'+ questionType +'"><div class="questionTitle">'+ serialNumber +'、'+ questionContent +'</div><span>('+ rate +')</span></div><div>'
					      + otionsHtml
					    +'</div></div>'
					    $('#questionnaire form').append(html);
					}else if(questionType == "单选"){
						var options = question.options;
						var otionsHtml = "";
						for(var j=0;j<options.length;j++){
							var option = options[j];
							var content = option.content;
							var id = option.id;
							otionsHtml += '<input type="radio" optionId="'+ id +'"  name="'+ questionId +'" value="'+ id +'" title="'+ content +'">'
						}
						var html = '<div class="layui-form-item none" sort="'+ i +'">'
				  		+'<div class="question" questionId="'+ questionId +'" type="'+ questionType +'"><div class="questionTitle">'+ serialNumber +'、'+ questionContent +'</div><span>('+ rate +')</span></div><div>'
					      + otionsHtml
					    +'</div></div>'
					    $('#questionnaire form').append(html);
					}
					$('#questionnaire .layui-form-item').eq(0).removeClass("none");
					form.render();
				}
				*/
			}
		}
	})
}
//展开所有问题
function showAllQuestions(){
	$('#questionnaire .layui-form-item').each(function(n,item){
		var temp = $(item);
		if(!temp.hasClass("none")){
			var current = temp.attr("sort");
			$('#questionnaire').attr("current",current);
		}
		temp.removeClass("none");
	})
	$(".questionnaireDiv").addClass("justTitle");
	$(".showAllQuestions").addClass("none");
	$(".showOneQuestion").removeClass("none");
	$('.questionPage').addClass("none");
	$('.submitQuestionnaire').removeClass("none");
}
//收起
function showOneQuestion(){
	var current = $('#questionnaire').attr("current");
	$('#questionnaire .layui-form-item').addClass("none");
	console.log(current);
	if(current){
		$('#questionnaire .layui-form-item[sort='+ current +']').removeClass("none");
	}else{
		$('#questionnaire .layui-form-item[sort=0]').removeClass("none");
	}
	$(".questionnaireDiv").removeClass("justTitle");
	$(".showAllQuestions").removeClass("none");
	$(".showOneQuestion").addClass("none");
	$('.questionPage').removeClass("none");
	
	var current = $('#questionnaire').attr("current");
	var questionNumber = $('#questionnaire').attr("questionNumber");
	if((parseInt(current)+1) !=questionNumber){
		$('.submitQuestionnaire').addClass("none");
	}
	
}
//下一题
$('.nextQuestion').on('click',function(){
	var current = $('#questionnaire').attr("current");
	var next =  parseInt(current)+1;
	$('#questionnaire').attr("current",next);
	$('#questionnaire .layui-form-item[sort='+ current+']').addClass("none");
	$('#questionnaire .layui-form-item[sort='+ next +']').removeClass("none");
	var questionNumber = $('#questionnaire').attr("questionNumber");
	if((next+1) == questionNumber){
		$('.nextQuestion').addClass("none");
		$('.submitQuestionnaire').removeClass("none");
	}else{
		$('.lastQuestion').removeClass("none");
	}
})
//上一题
$('.lastQuestion').on('click',function(){
	var current = $('#questionnaire').attr("current");
	var last =  parseInt(current)-1;
	$('#questionnaire').attr("current",last);
	$('#questionnaire .layui-form-item[sort='+ current+']').addClass("none");
	$('#questionnaire .layui-form-item[sort='+ last +']').removeClass("none");
	var questionNumber = $('#questionnaire').attr("questionNumber");
	if(last== 0){
		$('.lastQuestion').addClass("none");
	}else{
		$('.nextQuestion').removeClass("none");
	}
	$('.submitQuestionnaire').addClass("none");
})
//提交问卷
function submitQuestionnaire(){
	var list = new Array();
	var checkedOptions = new Array();
	var flag = false;
	$('#questionnaire .layui-form-item').each(function(n){
		var type = $(this).find(".question").attr("type");
		var questionId = $(this).find(".question").attr("questionId");
		if(type=="问答"){
			var value = $(this).find('textarea').val();
			if(value){
				var data = {"questionId":questionId,"reply":value};
				list.push(data);
			}else{
				var sort = parseInt($(this).attr("sort"))+1;
				layer.msg('问题'+ sort +'未填请检查！');
				flag = false;
				return false;
			}
		}else if(type=="单选"){
			var value = $('input[name='+ questionId +']:checked').val();
			if(value){
				checkedOptions.push(value);
			}else{
				var sort = parseInt($(this).attr("sort"))+1;
				layer.msg('问题'+ sort +'未填请检查！');
				flag = false;
				return false;
			}
		}else if(type=="多选"){
			var checked = new Array();
			$('input[name='+ questionId +']:checked').each(function(){
				var value = $(this).val();
				checked.push(value);
				checkedOptions.push(value);
			})
			if(checked.length <= 0){
				var sort = parseInt($(this).attr("sort"))+1;
				layer.msg('问题'+ sort +'未填请检查！');
				flag = false;
				return false;
			}
		}
		flag = true;
	})
	if(flag){
			list.push({"checkedOptions":checkedOptions});
			console.log(list);
		var url = "/sqzc/m/dept/questionnaire/saveQuestionReply";
		var	data = {"questionnaireId":$('#questionnaire').attr("questionnaireId"),"submitData":JSON.stringify(list)}
		common.ajaxFunc(url,data,'json',function(result){
			console.log(result)
			if (result.success) {
				var msg = result.msg;
				layer.msg(msg);
			}else{
				layer.msg("提交失败！");
			}
		});
	}
}
function hiddenImg(obj){
	$(obj).attr("style","display:none;");
}
</script>

</body></html>